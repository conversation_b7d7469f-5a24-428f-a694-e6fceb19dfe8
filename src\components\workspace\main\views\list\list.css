.listView {
    & > div {
        height: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }
}

.listView {
    --size-5: 1.25rem;
    --size-7: 1.75rem;
    --size-8: 2rem;
    --size-20: 5rem;
    --size-24: 6rem;
    --size-48: 12rem;
    --size-xs: 20rem;
    --size-sm: 24rem;
    --size-md: 28rem;
    --size-lg: 32rem;
    --size-xl: 36rem;
    --size-2xl: 42rem;
    --size-3xl: 48rem;
}

.listView .rowGrid {
    min-height: 0;
    min-width: 0;
    @apply gap-3 items-center flex;
    width: max-content;
    min-width: 100%;
}

.listView .header-row.rowGrid {
    width: max-content;
    min-width: 100%;
}

.listView .rowGrid > div {
    @apply overflow-hidden truncate font-medium;
    width: 200px;
    flex-shrink: 0;
    box-sizing: border-box;
    padding-left: 12px;
    padding-right: 12px;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
}

.listView .rowGrid > div.check {
    @apply w-8 flex-shrink-0;
}

.listView .rowGrid > div.fluid {
    min-width: 200px;
    max-width: 300px;
    flex: 0 0 auto;
    padding-left: 0;
}

.listView .header-row > div {
    box-sizing: border-box;
}

.listView .header-row {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: white;
}

.listView .header-row > div {
    font-weight: 700 !important;
}

/* NEW: Sticky horizontal scrollbar implementation */
.listView .scroll-wrapper {
    position: relative;
    height: 100%;
    overflow: hidden;
}

.listView .content-container {
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
}

.listView .horizontal-scroll-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    overflow-x: auto;
    overflow-y: hidden;
    z-index: 15;
    background: rgba(255, 255, 255, 0.95);
}

.listView .horizontal-scroll-content {
    height: 1px;
    width: max-content;
    min-width: 100%;
}

.listView .scroll-container {
    width: 100%;
    min-width: 100%;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    padding-bottom: 20px; /* Space for sticky horizontal scrollbar */
}

.listView .scrollBlockChild {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Sync horizontal scrolling between content and scrollbar */
.listView .content-horizontal-scroll {
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.listView .content-horizontal-scroll::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.listView .title-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

/* Disable all column field interactions - make fields static */
.listView .rowGrid > div:not(.check):not(.fluid) * {
    pointer-events: none !important;
    cursor: default !important;
    user-select: none !important;
}

/* Disable interactions in fluid column content too, but allow text selection */
.listView .rowGrid > div.fluid * {
    pointer-events: none !important;
    cursor: default !important;
}

/* Ensure row-level clicks still work for peek view */
.listView .rowGrid {
    cursor: pointer !important;
}

/* Allow interactive elements to receive pointer events - higher specificity */
.listView .rowGrid > div:not(.check):not(.fluid) .r-button-group,
.listView .rowGrid > div:not(.check):not(.fluid) .r-scannable-code,
.listView .rowGrid > div:not(.check):not(.fluid) .r-files,
.listView .rowGrid > div:not(.check):not(.fluid) button,
.listView .rowGrid > div:not(.check):not(.fluid) input,
.listView .rowGrid > div:not(.check):not(.fluid) select,
.listView .rowGrid > div:not(.check):not(.fluid) textarea,
.listView .rowGrid .r-button-group,
.listView .rowGrid .r-scannable-code,
.listView .rowGrid .r-files,
.listView .rowGrid button,
.listView .rowGrid input,
.listView .rowGrid select,
.listView .rowGrid textarea {
    pointer-events: auto !important;
    cursor: pointer !important;
    user-select: auto !important;
}

/* Prevent any other field component from being interactive */
.listView .rowGrid [data-field-component]:not(.r-button-group):not(.r-scannable-code):not(.r-files),
.listView .rowGrid [data-cell-component]:not(.r-button-group):not(.r-scannable-code):not(.r-files) {
    pointer-events: none !important;
    cursor: default !important;
    user-select: none !important;
}

@media (max-width: 640px) {
    .listView .rowGrid > div.fluid {
        min-width: 150px;
        max-width: 200px;
    }

    .listView .rowGrid > div:not(.fluid):not(.check) {
        width: 100px;
    }
}

@media (max-width: 768px) {
    .listView .rowGrid {
        @apply gap-2;
    }
}

/* Remove the old scroll area styles that conflict */
.listView [data-radix-scroll-area-viewport] > div {
    display: block !important;
    height: 100% !important;
}

.listView [data-radix-scroll-area-viewport] {
    overflow-x: hidden !important; /* Changed from auto to hidden */
}
