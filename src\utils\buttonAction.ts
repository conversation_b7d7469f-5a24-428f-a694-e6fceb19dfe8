import {Action, Property} from "@opendashboard-inc/integration-core";
import {DbCondition} from "opendb-app-db-utils/src/typings/db";
import {CampaignAttachment} from "@/typings/campaign";
import { sendDirectEmail } from "@/api/workspace";


export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  profilePhoto?: string;
}

export interface WorkspaceMember {
  user: User;
  role?: string;
  permissions?: string[];
}

export interface WorkspaceData {
  id: string;
  name?: string;
  domain?: string;
  [key: string]: string | undefined;
}

export interface DatabaseData {
  id: string;
  name?: string;
  definition?: {
    columnsMap?: object;
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

export interface RecordData {
  id: string;
  recordValues?: Record<string, any>;
  values?: Record<string, any>;
  record?: {
    recordValues?: Record<string, any>;
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

export interface TokenData {
  token?: string;
  sub?: string;
  userId?: string;
}

export interface ActionContext {
  record: RecordData;
  database?: DatabaseData;
  workspace?: {
    workspace?: WorkspaceData;
    id?: string;
    domain?: string;
  };
  token?: TokenData;
  user?: User;
  meta?: {
    databaseId?: string;
    [key: string]: unknown;
  };
  databaseId?: string;
  databaseStore?: any;
}

export interface ActionServices {
  updateRecordValues: (databaseId: string, recordIds: string[], values: Record<string, any>) => Promise<boolean>;
  deleteRecords: (databaseId: string, ids: string[]) => Promise<void>;
  setPeekRecord?: (databaseId: string, recordId: string) => void;
  pushPeek?: (recordId: string, databaseId: string) => void;
  confirm: (
    title: string, 
    message: string, 
    onConfirm: () => void, 
    onCancel?: () => void, 
    options?: object, 
    isDangerAction?: boolean,
    confirmButtonText?: string,
    cancelButtonText?: string
  ) => void;
  toast: {
    success: (message: string) => void;
    error: (message: string) => void;
    warning: (message: string) => void;
    info: (message: string) => void;
  };
  router: {
    push: (path: string) => void;
  };
  forceRender: () => void;
  sendMessage: (message: string) => void;
  showInputDialog: (title: string, message: string, callback: (value: string) => void) => void;
  directDeleteRecords?: (databaseId: string, ids: string[]) => Promise<void>;
}


export interface ButtonAction<TInput = object, TOutput = object> 
  extends Omit<Action<any, any>, 'run' | 'test'> {
  visibleIf?: DbCondition[];
  enabledIf?: DbCondition[];
  run: (props: TInput, context: ActionContext, services: ActionServices) => Promise<TOutput>;
}



export enum ButtonState {  
  HIDDEN = 'hidden',
  DISABLED = 'disabled', 
  ENABLED = 'enabled',
  ERROR = 'error'  
}

export interface ButtonActionResult {
  state: ButtonState;
  errors: string[];
  visible: boolean;
  enabled: boolean;
}



export interface SendEmailInput {
    senderId: string; // Sender dropdown (same as email campaign)
    subject: string;
    to: string;
    cc?: string; // Plain text, separated by comma
    bcc?: string; // Plain text, separated by comma
    body: string;
}

export interface SendEmailOutput {
  success: boolean;
  messageId: string;
}

export interface OpenUrlInput {
  url: string;
}

export interface OpenUrlOutput {
  success: boolean;
  url: string;
}

export interface PeekRecordInput {
  databaseId?: string; // Database dropdown, default to current one
  recordId?: string; // Optional record ID, defaults to current record
}

export interface PeekRecordOutput {
  success: boolean;
  recordId: string;
}

export interface UpdateRecordInput {
  databaseId?: string; // Database dropdown, default to current one
  recordIds: string; // Free text of record ids separated by comma
  updates: Record<string, any>; // Values to be updated
}

export interface UpdateRecordOutput {
  success: boolean;
  recordId: string;
  updatedFields: string[];
}

export interface DeleteRecordInput {
  databaseId?: string; // Database dropdown, default to current one
  recordIds?: string; // Optional comma-separated IDs, defaults to current record
}

export interface DeleteRecordOutput {
  success: boolean;
  recordId: string;
}

export interface ShowConfirmationInput {
  message: string; // Confirmation message
  successLabel: string; // Success label
  cancelLabel: string; // Cancel label
}

export interface ShowConfirmationOutput {
  success: boolean;
  confirmed: boolean;
}

export interface ShowToastInput {
  message: string;
}

export interface ShowToastOutput {
  success: boolean;
  toastId: string;
}

export interface SendNotificationInput {
  users: string; // ID of users to be notified
  message: string;
}

export interface SendNotificationOutput {
  success: boolean;
  notificationId: string;
}

export interface CallWorkflowInput {
  workflowId: string; // Workflow dropdown
  inputs?: Record<string, any>; // Inputs for on-demand trigger
}

export interface CallWorkflowOutput {
  success: boolean;
  workflowExecutionId: string;
  result?: object;
}

export interface ExpandRecordInput {
  databaseId?: string; // Database dropdown, default to current one
  recordId?: string; // Optional record ID, defaults to current record
}

export interface ExpandRecordOutput {
  success: boolean;
  recordId: string;
}

export interface ExecuteIntegrationActionInput {
  integration: string; // Integration dropdown
  connection?: string; // Connection dropdown (if required)
  action: string; // Action dropdown
  props: Record<string, any>; // Props of action to be executed
}

export interface ExecuteIntegrationActionOutput {
  success: boolean;
  result?: object;
}


export const updateRecord: ButtonAction<UpdateRecordInput, UpdateRecordOutput> = {
  identifier: 'updateRecord',
  displayName: 'Update Record',
  props: {
    databaseId: Property.ShortText({
      displayName: 'Database',
      description: 'Database to update records in',
      required: false
    }),
    recordIds: Property.ShortText({
      displayName: 'Record IDs (optional)',
      description: 'Comma-separated IDs. Defaults to the current record if empty.',
      required: false
    }),
    updates: Property.Json({
      displayName: 'Updates',
      description: 'Values to be updated',
      required: true
    })
  },
  run: async (props: UpdateRecordInput, context: ActionContext, services: ActionServices) => {
    console.log(`[Button Action] > Starting 'updateRecord' action.`);
    if (!props.updates || typeof props.updates !== 'object') {
      const errorMsg = "Update action missing updates parameter";
      services.toast.error(errorMsg);
      throw new Error(errorMsg);
    }

    const recordIds = (props.recordIds || context.record.id || '')
        .split(',')
        .map(id => id.trim())
        .filter(id => id);

    if (recordIds.length === 0) {
      const errorMsg = "No valid record IDs found to update.";
      services.toast.error(errorMsg);
      throw new Error(errorMsg);
    }

    const databaseId = props.databaseId ||
                      context.database?.id ||
                      context.meta?.databaseId ||
                      context.databaseId;

    if (!databaseId) {
      const errorMsg = "Missing database ID";
      services.toast.error(errorMsg);
      throw new Error(errorMsg);
    }

    const result = await services.updateRecordValues(databaseId, recordIds, props.updates);

    if (result) {
      const successMsg = `${recordIds.length} record(s) updated successfully`;
      console.log(`[Button Action] > 'updateRecord' successful.`);
      services.toast.success(successMsg);
      services.forceRender();
      return {
        success: true,
        recordId: recordIds[0],
        updatedFields: Object.keys(props.updates)
      };
    } else {
      const errorMsg = "Failed to update record(s)";
      console.error(`[Button Action] > 'updateRecord' failed.`);
      services.toast.error(errorMsg);
      throw new Error(errorMsg);
    }
  }
};

export const sendEmail: ButtonAction<SendEmailInput, SendEmailOutput> = {
    identifier: 'sendEmail',
    displayName: 'Send Email',
    // description: 'Send email from Gmail',
    props: {
        senderId: Property.ShortText({
            displayName: 'Sender',
            description: 'Same sender dropdown when creating an email campaign',
            required: true,
        }),
        to: Property.ShortText({
            displayName: 'To',
            description: 'Recipient email',
            required: true,
        }),
        subject: Property.ShortText({
            displayName: 'Subject',
            description: 'Email subject',
            required: true,
        }),
        body: Property.LongText({
            displayName: 'Body',
            description: 'Email content',
            required: true,
        }),
        cc: Property.ShortText({
            displayName: 'CCs',
            description: 'Plain text, separated by comma',
            required: false,
        }),
        bcc: Property.ShortText({
            displayName: 'BCCs',
            description: 'Plain text, separated by comma',
            required: false,
        })
    },
    run: async (props: SendEmailInput, context: ActionContext, services: ActionServices) => {
        console.log(`[Button Action] > Starting 'sendEmail' action.`);
        if (!props.senderId || !props.to || !props.subject || !props.body) {
            const errorMsg = 'Email requires: sender, recipient (to), subject, and body';
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }

        const workspaceId = context.workspace?.workspace || (context.workspace as any);
        const actualWorkspaceId = workspaceId?.id || '';
        const token = context.token?.token || '';

        if (!actualWorkspaceId) {
            const errorMsg = 'Workspace ID not found';
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }

        if (!token) {
            const errorMsg = 'Authentication token not found';
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }

        if (!props.to) {
            const errorMsg = 'Recipient email (To) is empty after processing variables.';
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }

        const emailData = {
            to: props.to,
            subject: props.subject,
            body: props.body,
            cc: props.cc,
            bcc: props.bcc,
            senderId: props.senderId
        };

        const result = await sendDirectEmail(token, actualWorkspaceId, emailData);

        if (result.error) {
            const errorMsg = `Failed to send email: ${result.error}`;
            console.error(`[Button Action] > 'sendEmail' failed.`, { error: result.error });
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }

        console.log(`[Button Action] > 'sendEmail' successful.`);
        services.toast.success('Email sent successfully!');
        return {
            success: true,
            messageId: 'email_' + Date.now()
        };
    },
};

export const openUrl: ButtonAction<OpenUrlInput, OpenUrlOutput> = {
    identifier: 'openUrl',
  displayName: 'Open URL',
  // description: 'Open URL in new tab',
  props: {
    url: Property.ShortText({
      displayName: 'URL',
      description: 'URL to open (supports variables)',
      required: true
    })
    // target: Property.StaticDropdown({
    //   displayName: 'Target',
    //   description: 'Where to open the URL',
    //   required: false,
    //   options: [
    //     {label: 'New Tab', value: '_blank'},
    //     {label: 'Same Tab', value: '_self'},
    //     {label: 'New Window', value: '_window'}
    //   ]
    // })
  },

    run: async (props: OpenUrlInput, context: ActionContext, services: ActionServices) => {
        console.log(`[Button Action] > Starting 'openUrl' action.`);
        if (!props.url) {
            services.toast.error("URL is required");
            throw new Error("URL is required");
        }

        try {
            let url = String(props.url)
                .trim()
                .replace(/\s+/g, '')
                .replace(/%20/g, '')
                .replace(/[\u200B-\u200D\uFEFF]/g, '')
                .replace(/\u00A0/g, '')
                .replace(/\{\{[^}]*\}\}/g, '')
                .replace(/@+/g, '')
                .replace(/\/+$/, '')
                .replace(/([^:])\/\/+/g, '$1/')
                .trim();
            
            if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('mailto:') && !url.startsWith('ftp://')) {
                url = 'https://' + url;
            }
            
            if (!url || url === 'https://' || url.length < 10) {
                services.toast.error("Invalid URL: " + url);
                throw new Error("Invalid URL: " + url);
            }
            
            console.log(`[Button Action] > 'openUrl' successful, returning URL: ${url}`);
            return {
                success: true,
                url
            };
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            services.toast.error("Failed to open URL: " + errorMessage);
            throw new Error("Failed to open URL: " + errorMessage);
        }
    }
};

export const deleteRecord: ButtonAction<DeleteRecordInput, DeleteRecordOutput> = {
    identifier: 'deleteRecord',
    displayName: 'Delete Record',
    props: {
        databaseId: Property.ShortText({
            displayName: 'Database (optional)',
            description: 'Defaults to the current database if left empty.',
            required: false
        }),
        recordIds: Property.ShortText({
            displayName: 'Record IDs (optional)',
            description: 'Comma-separated IDs. Defaults to the current record if empty.',
            required: false
        })
    },
    run: async (props: DeleteRecordInput, context: ActionContext, services: ActionServices) => {
        try {
            const isForeignKey = !!props.databaseId && props.databaseId !== context.databaseId;
            const databaseId = props.databaseId || context.databaseId;
            let recordIdsString;

            if (isForeignKey) {
                recordIdsString = props.recordIds;
                if (!recordIdsString) {
                    services.toast.error("Record IDs are required when deleting records from another database.");
                    throw new Error("Record IDs are required for foreign key delete.");
                }
            } else {
                recordIdsString = props.recordIds || context.record?.id;
            }

            const idsToDelete = (recordIdsString || '')
                .split(',')
                .map(id => id.trim())
                .filter(id => id);

            if (idsToDelete.length === 0) {
                services.toast.error("Could not find any records to delete. If you're using a column reference, please ensure it has a value in this row.");
                throw new Error("No record IDs found to delete.");
            }

            if (!databaseId) {
                services.toast.error("Missing database ID");
                throw new Error("Missing database ID");
            }

            // Pre-flight check: ensure all records exist before attempting deletion.
            const targetDatabase = context.databaseStore?.[databaseId];
            const notFoundIds = idsToDelete.filter(id => !targetDatabase?.recordsIdMap?.[id]);

            if (notFoundIds.length > 0) {
                const dbName = targetDatabase?.database?.name || databaseId;
                services.toast.error(`The following record IDs were not found in "${dbName}": ${notFoundIds.join(', ')}. No records were deleted.`);
                throw new Error(`Records not found for deletion: ${notFoundIds.join(', ')}`);
            }

            const confirmationMessage = `Are you sure you want to delete ${idsToDelete.length} record(s)? This cannot be undone.`;

            return new Promise<DeleteRecordOutput>((resolve, reject) => {
                services.confirm(
                    'Delete Record',
                    confirmationMessage,
                    async () => {
                        try {
                            if (services.directDeleteRecords) {
                                await services.directDeleteRecords(databaseId, idsToDelete);
                                services.toast.success(`${idsToDelete.length} record(s) deleted successfully`);
                                services.forceRender();
                                resolve({
                                    success: true,
                                    recordId: idsToDelete[0]
                                });
                            } else {
                                services.toast.error("Delete function not available");
                                reject(new Error("Delete function not available"));
                            }
                        } catch (error) {
                            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                            services.toast.error("Failed to delete record: " + errorMessage);
                            reject(new Error("Failed to delete record: " + errorMessage));
                        }
                    },
                    () => {
                        services.toast.info("Deletion cancelled");
                        reject(new Error("Deletion cancelled"));
                    },
                    undefined,
                    true
                );
            });
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            services.toast.error("Failed to delete record: " + errorMessage);
            throw new Error("Failed to delete record: " + errorMessage);
        }
    }
};

export const showConfirmation: ButtonAction<ShowConfirmationInput, ShowConfirmationOutput> = {
    identifier: 'showConfirmation',
    displayName: 'Show Confirmation',
    props: {
        message: Property.LongText({
            displayName: 'Confirmation message',
            description: 'Confirmation message',
            required: true
        }),
        successLabel: Property.ShortText({
            displayName: 'Success label',
            description: 'Success label',
            required: true
        }),
        cancelLabel: Property.ShortText({
            displayName: 'Cancel label',
            description: 'Cancel label',
            required: true
        })
    },
    run: async (props: ShowConfirmationInput, context: ActionContext, services: ActionServices) => {
        const title = 'Confirmation';
        const message = props.message || 'Are you sure?';
        const confirmButtonText = props.successLabel || 'Confirm';
        const cancelButtonText = props.cancelLabel || 'Cancel';
        
        console.log(`[Button Action] > Starting 'showConfirmation' action.`);
        return new Promise<ShowConfirmationOutput>((resolve) => {
            services.confirm(
                title,
                message,
                () => {
                    console.log(`[Button Action] > User confirmed 'showConfirmation'.`);
                    services.toast.success("Action confirmed");
                    resolve({
                        success: true,
                        confirmed: true
                    });
                },
                () => {
                    console.log(`[Button Action] > User cancelled 'showConfirmation'.`);
                    services.toast.info(`Action cancelled: ${cancelButtonText}`);
                    resolve({
                        success: true,
                        confirmed: false
                    });
                },
                undefined,
                false,
                confirmButtonText,
                cancelButtonText
            );
        });
    }
};

export const showToast: ButtonAction<ShowToastInput, ShowToastOutput> = {
    identifier: 'showToast',
    displayName: 'Show Toast',
    // description: 'Show a toast notification to the user',
    props: {
        message: Property.ShortText({
            displayName: 'Message',
            // description: 'Toast notification message',
            required: true
        }),
        type: Property.StaticDropdown({
            displayName: 'Type',
            // description: 'Toast notification type',
            required: true,
            options: [
                {label: 'Success', value: 'success'},
                {label: 'Error', value: 'error'},
                {label: 'Warning', value: 'warning'},
                {label: 'Info', value: 'info'}
            ]
        }),
        duration: Property.Number({
            displayName: 'Duration (ms)',
            // description: 'How long the toast should be visible (default: 3000ms)',
            required: false
        })
    },
    run: async (props: ShowToastInput, context: ActionContext, services: ActionServices) => {
        const message = props.message;

        // Default to success toast
        console.log(`[Button Action] > Starting 'showToast' action with message: "${message}"`);
        services.toast.success(message);

        return {
            success: true,
            toastId: 'toast_' + Date.now()
        };
    }
};

export const sendNotification: ButtonAction<SendNotificationInput, SendNotificationOutput> = {
    identifier: 'sendNotification',
    displayName: 'Send Notification',
    props: {
        users: Property.ShortText({
            displayName: 'Users',
            description: 'ID of users to be notified',
            required: true
        }),
        message: Property.LongText({
            displayName: 'Message',
            description: 'Notification message content',
            required: true
        })
    },
    run: async (props: SendNotificationInput, context: ActionContext, services: ActionServices) => {
        const { createNotification, getWorkspaceMembers } = await import('@/api/workspace');
        
        console.log(`[Button Action] > Starting 'sendNotification' action.`);
        if (!props.users || !props.message) {
            services.toast.error("Notification requires users and message");
            throw new Error("Notification requires users and message");
        }

        try {
            let targetUserId: string | undefined = undefined;
            const recipientIdentifier = props.users || '';

            if (!recipientIdentifier) {
                services.toast.error("Recipient user is empty after processing variables.");
                throw new Error("Recipient user is empty.");
            }

            // If the identifier looks like a UUID, assume it's a direct user ID.
            // This is a common case for @currentuser
            if (/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(recipientIdentifier)) {
                targetUserId = recipientIdentifier;
            } else {
                // Otherwise, search through workspace members
                const token = context.token?.token;
                const workspaceData = context.workspace?.workspace || (context.workspace as any);
                const actualWorkspaceId = workspaceData?.id;

                const membersResponse = await getWorkspaceMembers(token || '', actualWorkspaceId || '');

                if (membersResponse.error || !membersResponse.data?.data?.members) {
                    services.toast.error("Failed to load workspace members for user resolution");
                    throw new Error("Failed to load workspace members for user resolution");
                }

                const members = membersResponse.data.data.members;
                
                let foundUser = members.find((m: any) => m.user?.id === recipientIdentifier) ||
                               members.find((m: any) => m.user?.email?.toLowerCase() === recipientIdentifier.toLowerCase()) ||
                               members.find((m: any) => {
                                   const user = m.user;
                                   if (!user) return false;
                                   
                                   const fullName = `${user.firstName || ''} ${user.lastName || ''}`.trim().toLowerCase();
                                   const firstName = user.firstName?.toLowerCase() || '';
                                   const lastName = user.lastName?.toLowerCase() || '';
                                   const recipientLower = recipientIdentifier.toLowerCase();
                                   
                                   return fullName === recipientLower || firstName === recipientLower || lastName === recipientLower;
                               });
                
                if (foundUser) {
                    targetUserId = foundUser.user.id;
                }
            }

            if (!targetUserId) {
                services.toast.error(`User "${recipientIdentifier}" not found in this workspace.`);
                throw new Error(`User "${recipientIdentifier}" not found`);
            }
            

            const workspaceData = context.workspace?.workspace || (context.workspace as any);
            const actualWorkspaceId = workspaceData?.id || '';
            const token = context.token?.token || '';

            const databaseId = (context.database as DatabaseData)?.id || 
                               context.meta?.databaseId || 
                               context.databaseId;
            
            let notificationLink = undefined;
            if (workspaceData?.domain) {
                if (databaseId) {
                    notificationLink = `/${workspaceData.domain}/databases/${databaseId}`;
                } else {
                    notificationLink = `/${workspaceData.domain}`;
                }
            }

            const notificationData = {
                userId: targetUserId || '',
                title: 'Notification',
                message: props.message,
                link: notificationLink
            };
            
            const result = await createNotification(token || '', actualWorkspaceId || '', notificationData);
            
            if (result && !result.error) {
                // const targetName = props.recipients ? `user "${props.recipients}"` : "yourself";
                // services.toast.success(`Notification sent successfully to ${targetName}`);
                console.log(`[Button Action] > 'sendNotification' successful.`);
                services.toast.success("Notification sent successfully");
                return {
                    success: true,
                    notificationId: 'notification_' + Date.now()
                };
            } else {
                services.toast.error("Failed to send notification");
                throw new Error("Failed to send notification");
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            services.toast.error(`Failed to send notification: ${errorMessage}`);
            throw new Error(`Failed to send notification: ${errorMessage}`);
        }
    }
};

export const callWorkflow: ButtonAction<CallWorkflowInput, CallWorkflowOutput> = {
    identifier: 'callWorkflow',
    displayName: 'Call Workflow',
    props: {
        workflowId: Property.StaticDropdown({
            displayName: 'Workflow',
            description: 'Select a workflow with an on-demand trigger.',
            required: true,
            options: [] // Options are populated dynamically in the editor
        }),
        inputs: Property.Json({
            displayName: 'Inputs',
            description: 'Inputs for the on-demand trigger, if required.',
            required: false
        })
    },
    run: async (props: CallWorkflowInput, context: ActionContext, services: ActionServices) => {
        const { createWorkflowInstance } = await import('@/api/workflow');
        
        console.log(`[Button Action] > Starting 'callWorkflow' action.`);
        if (!props.workflowId) {
            const errorMsg = "Workflow ID is required";
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }

        const workspaceId = context.workspace?.workspace?.id;
        const token = context.token?.token;

        if (!workspaceId || !token) {
            const errorMsg = "Workspace ID or token not found.";
            services.toast.error(errorMsg);
            throw new Error(errorMsg);
        }
        
        let inputs = {};
        if (props.inputs) {
            try {
                inputs = typeof props.inputs === 'string' ? JSON.parse(props.inputs) : props.inputs;
            } catch (e) {
                const errorMsg = `Invalid JSON in workflow inputs: ${(e as Error).message}`;
                console.error(`[Button Action] > 'callWorkflow' failed due to invalid JSON.`, { inputs: props.inputs });
                services.toast.error(errorMsg);
                throw new Error(errorMsg);
            }
        }

        try {
            console.log(`[Button Action] > Calling workflow: ${props.workflowId} with inputs:`, { inputs });
            const result = await createWorkflowInstance(token, workspaceId, props.workflowId, { data: inputs });

            if (result.error) {
                const errorMsg = `Failed to call workflow: ${result.error}`;
                console.error(`[Button Action] > 'callWorkflow' API call failed.`, { error: result.error });
                services.toast.error(errorMsg);
                throw new Error(errorMsg);
            }

            console.log(`[Button Action] > 'callWorkflow' successful.`);
            services.toast.success(`Workflow "${props.workflowId}" started successfully.`);
            
            return {
                success: true,
                workflowExecutionId: result.data?.data?.instance.id || 'exec_' + Date.now(),
                result: result.data?.data?.instance
            };
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            console.error(`[Button Action] > 'callWorkflow' action failed.`, { error: errorMessage });
            services.toast.error("Failed to call workflow: " + errorMessage);
            throw new Error("Failed to call workflow: " + errorMessage);
        }
    }
};

export const expandRecord: ButtonAction<ExpandRecordInput, ExpandRecordOutput> = {
    identifier: 'expandRecord',
    displayName: 'Expand Record',
    props: {
        databaseId: Property.ShortText({
            displayName: 'Database (optional)',
            description: 'Defaults to the current database if left empty.',
            required: false
        }),
        recordId: Property.ShortText({
            displayName: 'Record ID (optional)',
            description: 'Defaults to the current record if empty.',
            required: false
        })
    },
    run: async (props: ExpandRecordInput, context: ActionContext, services: ActionServices) => {
        const workspaceData = context.workspace?.workspace || (context.workspace as any);
        if (!workspaceData?.domain) {
            services.toast.error("Missing workspace domain");
            throw new Error("Missing workspace domain");
        }
        
        const isForeignKey = !!props.databaseId && props.databaseId !== context.databaseId;
        const databaseId = props.databaseId || context.databaseId;
        let recordId;

        if (isForeignKey) {
            // Peeking a record in ANOTHER database. Record ID from props is MANDATORY.
            recordId = props.recordId;
            if (!recordId) {
                services.toast.error("Record ID is required when expanding a record from another database.");
                throw new Error("Record ID is required for foreign key expand.");
            }
        } else {
            // Peeking a record in the CURRENT database. Record ID is optional.
            recordId = props.recordId || context.record?.id;
        }

        if (!databaseId) {
            services.toast.error("Missing database ID");
            throw new Error("Missing database ID");
        }
        
        if (!recordId) {
            services.toast.error("Could not find a record to expand. If you're using a column reference, please ensure it has a value in this row.");
            throw new Error("No record ID in context.");
        }

        if (recordId.includes(',')) {
            services.toast.error("The 'Expand Record' action only works with a single Record ID.");
            throw new Error("Multiple record IDs not supported for expand action.");
        }

        // Pre-flight check: ensure the record exists in the store before peeking.
        const targetDatabase = context.databaseStore?.[databaseId];
        if (!targetDatabase?.recordsIdMap?.[recordId]) {
            const dbName = targetDatabase?.database?.name || databaseId;
            services.toast.error(`The record ID "${recordId}" was not found in the database "${dbName}".`);
            throw new Error(`Record not found for expand: ${recordId} in ${dbName}`);
        }
        //  services.router.push(`/${workspaceData.domain}/databases/${databaseId}/records/${recordId}`);
        window.open(`/${workspaceData.domain}/databases/${databaseId}/records/${recordId}`, '_blank', 'noopener,noreferrer');
        
        return {
            success: true,
            recordId,
        };
    }
};

export const peekRecord: ButtonAction<PeekRecordInput, PeekRecordOutput> = {
    identifier: 'peekRecord',
    displayName: 'Peek Record',
    props: {
        databaseId: Property.ShortText({
            displayName: 'Database (optional)',
            description: 'Defaults to the current database if left empty.',
            required: false
        }),
        recordId: Property.ShortText({
            displayName: 'Record ID (optional)',
            description: 'Defaults to the current record if empty.',
            required: false
        })
    },
    run: async (props: PeekRecordInput, context: ActionContext, services: ActionServices) => {
        const isForeignKey = !!props.databaseId && props.databaseId !== context.databaseId;
        const databaseId = props.databaseId || context.databaseId;
        let recordId;

        if (isForeignKey) {
            // Peeking a record in ANOTHER database. Record ID from props is MANDATORY.
            recordId = props.recordId;
            if (!recordId) {
                services.toast.error("Record ID is required when peeking into another database.");
                throw new Error("Record ID is required for foreign key peek.");
            }
        } else {
            // Peeking a record in the CURRENT database. Record ID is optional.
            recordId = props.recordId || context.record?.id;
        }

        if (!databaseId) {
            services.toast.error("Could not find a database to peek in the current context.");
            throw new Error("No database ID in context.");
        }

        if (!recordId) {
            services.toast.error("Could not find a record to peek. If you're using a column reference, please ensure it has a value in this row.");
            throw new Error("No record ID found to peek.");
        }

        if (recordId.includes(',')) {
            services.toast.error("The 'Peek Record' action only works with a single Record ID.");
            throw new Error("Multiple record IDs not supported for peek action.");
        }

        // Pre-flight check: ensure the record exists in the store before peeking.
        const targetDatabase = context.databaseStore?.[databaseId];
        if (!targetDatabase?.recordsIdMap?.[recordId]) {
            const dbName = targetDatabase?.database?.name || databaseId;
            services.toast.error(`The record ID "${recordId}" was not found in the database "${dbName}".`);
            throw new Error(`Record not found for peek: ${recordId} in ${dbName}`);
        }

        if (services.pushPeek) {
            services.pushPeek(recordId, databaseId);
        } else if (services.setPeekRecord) {
            // Fallback to the single peek view if the stack is not available.
            services.setPeekRecord(databaseId, recordId);
        } else {
            services.toast.error("Peek view functionality not available.");
            throw new Error("Peek view functionality not available.");
        }

        return {
            success: true,
            recordId,
        };
    }
};

export const executeIntegrationAction: ButtonAction<ExecuteIntegrationActionInput, ExecuteIntegrationActionOutput> = {
    identifier: 'executeIntegrationAction',
    displayName: 'Execute Integration Action',
    // description: 'Execute an integration action',
    props: {
        integration: Property.ShortText({
            displayName: 'Integration',
            description: 'Integration dropdown',
            required: true
        }),
        connection: Property.ShortText({
            displayName: 'Connection',
            description: 'Connection dropdown (if required)',
            required: false
        }),
        action: Property.ShortText({
            displayName: 'Action',
            description: 'Action dropdown',
            required: true
        }),
        props: Property.Json({
            displayName: 'Props',
            description: 'Props of action to be executed',
            required: true
        })
    },
    run: async (props: ExecuteIntegrationActionInput, context: ActionContext, services: ActionServices) => {
        console.log(`[Button Action] > Starting 'executeIntegrationAction' action.`);
        if (!props.integration || !props.action) {
            services.toast.error("Integration action requires integration and action");
            throw new Error("Integration action requires integration and action");
        }

        try {
            // This would need to be implemented based on the integration execution API
            console.log(`[Button Action] > 'executeIntegrationAction' successful.`);
            services.toast.success("Integration action executed successfully");
            return {
                success: true,
                result: { executed: true }
            };
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            services.toast.error(`Failed to execute integration action: ${errorMessage}`);
            throw new Error(`Failed to execute integration action: ${errorMessage}`);
        }
    }
};

export const buttonActions = {
    updateRecord,
    sendEmail,
    openUrl,
    deleteRecord,
    showConfirmation,
    showToast,
    sendNotification,
    callWorkflow,
    expandRecord,
    peekRecord,
    executeIntegrationAction
};


export const getButtonAction = (identifier: string): ButtonAction | undefined => {
    const action = buttonActions[identifier as keyof typeof buttonActions] as ButtonAction<any, any> | undefined;
    return action;
};

export const getButtonActionIdentifiers = (): string[] => {
    const identifiers = Object.keys(buttonActions);
    return identifiers;
};

