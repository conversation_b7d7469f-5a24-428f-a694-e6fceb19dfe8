import {BoardColumn} from "@/components/workspace/main/views/board/components/boardColumn";
import {HiddenColumns} from "@/components/workspace/main/views/board/components/hiddenColumns";
import React, {useState} from "react";
import {DndContext, DragEndEvent, DragOverEvent, DragOverlay, DragStartEvent, PointerSensor, useSensor, useSensors} from "@dnd-kit/core";
import {arrayMove, SortableContext} from "@dnd-kit/sortable";
import {NewColumn} from "@/components/workspace/main/views/board/components/newColumn";
import {createPortal} from "react-dom";
import {ItemCard} from "@/components/workspace/main/views/board/components/itemCard";
import {BoardViewRenderProps} from "@/components/workspace/main/views/board/board";
import {useWorkspace} from "@/providers/workspace";
import {DatabaseFieldDataType, DbRecordSort, MagicColumn, Match, RecordValues, SelectColumn, SelectOption, Sort} from "opendb-app-db-utils/lib/typings/db";
import {CompareOperator} from "opendb-app-db-utils/lib/methods/compare";
import {DatabaseRecordStoreItem} from "@/typings/utilities";
import {useViews, useViewFiltering, useViewSelection} from "@/providers/views";
import {Color} from "opendb-app-db-utils/lib/typings/color";
import {DataViewRow, filterAndSortRecords} from "@/components/workspace/main/views/table";
import {BoardGroupItemProps, BoardViewDefinition} from "opendb-app-db-utils/lib/typings/view";
import {ContentLocked} from "@/components/workspace/main/views/common/contentLocked";
import {arrayDeDuplicate, removeAllArrayItem} from "opendb-app-db-utils/lib";
import {arrayAddElementAdjacent} from "opendb-app-db-utils/lib/methods/array";
import {generateUUID} from "opendb-app-db-utils/lib/methods/string";
import {DatabaseConstants} from "@/components/workspace/main/views/table/renderer/common/addColumn";
import {useMaybeShared} from "@/providers/shared";
import {useMaybeTemplate} from "@/providers/template";
import {AddRecordModal} from "@/components/workspace/main/views/AddRecordModal";
import {useStackedPeek} from "@/hooks/useStackedPeek";
import {useViewContext} from "@/components/workspace/main/views/ViewsRootLayout";

export type Id = string | number;
export type Column = {
    id: string;
    title: string;
    color?: Color
    value: string
};

const UngroupedKey = 'ungrouped'

export type Item = {
    id: string | number;
    columnId: Id;
    content: string;
};

export interface BoardItems {
    [columnId: string]: string[]
}


export const BoardContainer = (props: BoardViewRenderProps & {
    database: DatabaseRecordStoreItem
}) => {
    const {databaseStore} = useWorkspace()
    const {definition} = props

    const database = databaseStore[definition.databaseId]
    const groupColumn: SelectColumn = database.database.definition.columnsMap[definition.groupByIds[0]] as SelectColumn

    if (!groupColumn) return null;

    return <BoardContent {...props} />
}

const BoardContent = (props: BoardViewRenderProps & {
    database: DatabaseRecordStoreItem
}) => {
    const {databaseStore, databaseErrorStore, members, workspace} = useWorkspace()
    const {definition} = props
    const {updateViewDefinition, updateRecordValues, updateDatabaseColumn, createRecords, cache} = useViews()
    const {filter: viewFilter, search: viewSearch} = useViewFiltering()
    const {selectedIds, setSelectedIds} = useViewSelection()
    const {openRecord} = useStackedPeek()
    const {context} = useViewContext();

    // State for AddRecordModal
    const [showAddModal, setShowAddModal] = useState(false)
    const [selectedGroupId, setSelectedGroupId] = useState<string>('')

    const recordIds: string[] = cache.getCache(DatabaseConstants.NewlyCreatedColumnKey, [])
    console.log('Newly created on render:', {recordIds})

    const database = databaseStore[definition.databaseId]
    const groupColumn: SelectColumn = database.database.definition.columnsMap[definition.groupByIds[0]] as SelectColumn

    // if (!groupColumn) return null;
    const shared = useMaybeShared()
    const maybeShared = shared

    const editable = !definition.lockContent && !shared

    const maybeTemplate = useMaybeTemplate()

    let canEditStructure = (!maybeTemplate && !maybeShared && !definition.lockContent && editable)
    let canEditData: boolean = (!maybeTemplate && !maybeShared && !definition.lockContent && editable)

    definition.filter = definition.filter || {conditions: [], match: Match.All}
    definition.sorts = definition.sorts || []
    definition.groupOrder = definition.groupOrder || []
    definition.groupItemsProps = definition.groupItemsProps || {}
    definition.columnsOrder = definition.columnsOrder || []
    definition.columnPropsMap = definition.columnPropsMap || {}

    // Use context-aware filter and search
    const filter = viewFilter
    const search = viewSearch
    let titleColId = database.database.definition.titleColumnId || ''

    const fixColumnPropsMap = () => {
        // by default show the title column and 4 more and hide the rest
        if (!titleColId) {
            for (const column of Object.values(database.database.definition.columnsMap)) {
                if (column.type === DatabaseFieldDataType.Text) {
                    titleColId = column.id
                    break
                }
            }
        }
        if (!titleColId) titleColId = database.database.definition.columnIds[0]

        let {columnsOrder, columnPropsMap} = definition
        // columnPropsMap = columnPropsMap || {}
        // columnsOrder = Array.isArray(columnsOrder) ? columnsOrder : []
        // columnPropsMap = columnPropsMap || {}
        let visibleColumns = Object.values(columnPropsMap).filter(props => !props.isHidden).length
        const visibleOnLoad = visibleColumns

        for (const column of Object.values(database.database.definition.columnsMap)) {
            const id = column.id
            if (!columnsOrder.includes(id)) columnsOrder.push(id)
            if (!columnPropsMap[id]) columnPropsMap[id] = {isHidden: true}
            if (id !== titleColId && visibleOnLoad === 0 && visibleColumns < 5) {
                columnPropsMap[id] = {isHidden: false}
                visibleColumns++
            }
        }
    }

    fixColumnPropsMap()

    const buildCols = () => {
        const columns: Column[] = []
        const hiddenColumns: Column[] = []

        // let {columnsOrder, columnPropsMap} = definition
        const groupOrder = Array.isArray(definition.groupOrder) ? definition.groupOrder : []
        const groupItemsProps = definition.groupItemsProps ? definition.groupItemsProps : {}

        for (const key of groupColumn.optionIds) {
            if (!groupOrder.includes(key)) groupOrder.push(key)
            if (!groupItemsProps[key]) groupItemsProps[key] = {
                isHidden: false,
                itemsOrder: []
            }
        }
        let ungroupExists = false

        for (const id of groupOrder) {
            let col: Column;
            const option = groupColumn.optionsMap[id]


            if (id === UngroupedKey) {
                col = {
                    id: UngroupedKey,
                    title: 'Ungrouped',
                    value: UngroupedKey
                }
                groupItemsProps[UngroupedKey] = groupItemsProps[id] || {
                    isHidden: false,
                    itemsOrder: []
                }
                ungroupExists = true
            } else if (option) {
                col = {
                    id,
                    title: option.title,
                    color: option.color,
                    value: id
                }
            } else continue
            if (groupItemsProps[id].isHidden) hiddenColumns.push(col)
            else columns.push(col)
        }
        if (!ungroupExists) {
            const col: Column = {
                id: UngroupedKey,
                title: 'Ungrouped',
                value: ''
            }
            columns.unshift(col)
            groupItemsProps[UngroupedKey] = {
                isHidden: false,
                itemsOrder: []
            }
        }

        definition.groupOrder = [...columns.map(c => c.id), ...hiddenColumns.map(c => c.id)]

        return {columns, hiddenColumns}

    }

    const updateDefinition = async (update: Partial<BoardViewDefinition>) => {
        await updateViewDefinition(props.view.id, props.view.pageId, update)
    }
    const getProcessedRecords = () => {
        const sortOptions: DbRecordSort[] = []
        sortOptions.push({columnId: MagicColumn.CreatedAt, order: Sort.Desc})

        const {rows} = filterAndSortRecords(
            database,
            members,
            databaseStore,
            definition.filter,
            filter,
            sortOptions,
            workspace.workspaceMember.userId
        )


        return rows
    }

    const getFinalBoardItems = (filteredRows: DataViewRow[], groupOrder: string[]) => {
        const boardItems: BoardItems = {}

        if (!definition.groupItemsProps) definition.groupItemsProps = {}

        // Initialize boardItems for each column in columnsOrder with empty arrays
        for (let id of groupOrder) {
            boardItems[id] = [];
        }

        const groupColId = groupColumn.id;

        const ungroupedIds: string[] = []

        const rowIdsMap: {
            [id: string]: DataViewRow
        } = {}

        // Populate boardItems with row ids based on the groupColId values found in each row
        for (const row of filteredRows) {
            let groupId = '';
            let value = row.record.recordValues[groupColId] as (string[] | undefined)
            if (!Array.isArray(value)) value = []
            let isGrouped = false
            if (value && value.length > 0) {
                for (const id of groupOrder) {
                    if (value.includes(id)) {
                        boardItems[id].push(row.id)
                        isGrouped = true

                        groupId = id
                    }
                }
            }
            if (!isGrouped) {
                ungroupedIds.push(row.id)
                groupId = UngroupedKey
            }

            rowIdsMap[row.id] = {
                id: row.id,
                record: row.record,
                processedRecord: row.processedRecord,
                updatedAt: row.updatedAt
            }

        }

        // Final boardItems that respects the order and content of groupItemsOrder
        const finalBoardItems: BoardItems = {};

        for (let id of groupOrder) {
            finalBoardItems[id] = [];

            // if (!definition.groupItemsProps[id])
            // Retrieve the user-defined order for the current column
            const groupItemProps = definition.groupItemsProps[id] || {
                itemsOrder: []
            };
            if (!groupItemProps.itemsOrder) groupItemProps.itemsOrder = []

            const currentIdsSet = new Set(boardItems[id]);

            // Add items in the order from groupItemsOrder, respecting the existence of IDs in boardItems
            for (const itemId of groupItemProps.itemsOrder) {
                if (currentIdsSet.has(itemId)) {
                    finalBoardItems[id].push(itemId);
                }
            }

            // Add remaining items from boardItems that were not included but maintain the original order
            for (const itemId of boardItems[id]) {
                if (!groupItemProps.itemsOrder.includes(itemId)) {
                    finalBoardItems[id].push(itemId);
                }
            }
            groupItemProps.itemsOrder = finalBoardItems[id]
        }
        finalBoardItems[UngroupedKey] = ungroupedIds

        if (!definition.groupItemsProps[UngroupedKey]) definition.groupItemsProps[UngroupedKey] = {isHidden: false, itemsOrder: []}
        definition.groupItemsProps[UngroupedKey].itemsOrder = ungroupedIds

        return {boardItems: finalBoardItems, rowIdsMap};

    }

    const filteredRows = getProcessedRecords()

    const finalBoardItems = () => {
        let rows = filteredRows
        if (search && search.trim()) {
            rows = filteredRows.filter(r => r.processedRecord.valuesText.toLowerCase().includes(search.trim().toLowerCase()))
        }
        return getFinalBoardItems(rows, definition.groupOrder)
    }

    // const columns =
    const {columns, hiddenColumns} = buildCols()
    const boardItems = finalBoardItems()

    // const [columns, setColumns] = useState<Column[]>(board.cols);
    const columnsId = columns.map(c => c.id);

    // const [items, setItems] = useState<Item[]>(board.items);

    const [activeColumn, setActiveColumn] = useState<Column | null>(null);

    const [activeItem, setActiveItem] = useState<DataViewRow | null>(null);

    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 10,
            },
        })
    );

    // function createItem(columnId: Id) {
    //     const newItem: Item = {
    //         id: generateId(),
    //         columnId,
    //         content: `Item ${items.length + 1}`,
    //     };
    //
    //     setItems([...items, newItem]);
    // }

    // function deleteItem(id: Id) {
    //     const newItems = items.filter((item) => item.id !== id);
    //     setItems(newItems);
    // }


    function createNewColumn(name: string, color: Color) {
        const option: SelectOption = {
            id: generateUUID(),
            title: name,
            color
        }
        groupColumn.optionIds.push(option.id)
        groupColumn.optionsMap[option.id] = option

        updateDatabaseColumn(database.database.id, groupColumn.id, groupColumn.type, {
            optionsMap: groupColumn.optionsMap,
            optionIds: groupColumn.optionIds
        })
    }

    function deleteColumn(id: Id) {
        // const filteredColumns = columns.filter((col) => col.id !== id);
        // setColumns(filteredColumns);
        //
        // const newItems = items.filter((t) => t.columnId !== id);
        // setItems(newItems);
    }

    function updateColumn(id: string, update: Partial<BoardGroupItemProps>) {
        if (!definition.groupItemsProps) return

        let groupProps = definition.groupItemsProps?.[id]
        if (groupProps) {
            groupProps = {...groupProps, ...update}
            definition.groupItemsProps[id] = groupProps
            updateDefinition({groupItemsProps: definition.groupItemsProps}).then()
        }
    }

    const handleAddRecord = (groupId: string) => {
        // Check if we're in record tab context
        const isRecordTab = context === 'record_tab'
        
        if (isRecordTab) {
            // In record tabs: Use smart modal
            setSelectedGroupId(groupId)
            setShowAddModal(true)
        } else {
            // In main views: Direct record creation (original behavior)
            handleDirectBoardAdd(groupId)
        }
    }

    const handleDirectBoardAdd = async (groupId: string) => {
        const values: RecordValues = {}
        if (groupId !== UngroupedKey) {
            values[groupColumn.id] = [groupId]
        }
        
        await createRecords(database.database.id, [values])
    }

    const handleRecordCreated = (recordId: string) => {
        // Automatically open the newly created record using our stacked peek logic
        openRecord(recordId, database.database.id)
        setShowAddModal(false)
    }

    // Create view filter based on selected group (for pre-population)
    const getGroupFilter = () => {
        if (!selectedGroupId || selectedGroupId === UngroupedKey) {
            return undefined
        }
        
        return {
            conditions: [{
                columnId: groupColumn.id,
                op: CompareOperator.Equals,
                value: [selectedGroupId]
            }],
            match: Match.All
        }
    }

    // useEffect(() => {
    //     const recordIds: string[] = cache.getCache(DatabaseConstants.NewlyCreatedColumnKey, [])
    //     console.log('Newly created in effect:', {recordIds})
    //     // boardItems.rowIdsMap
    //     // const groupProps = definition.groupItemsProps?.[overColumnId]
    //     // if (groupProps) {
    //     //     groupProps.itemsOrder = groupProps.itemsOrder || []
    //     //     if (overId) {
    //     //         groupProps.itemsOrder = arrayAddElementAdjacent(groupProps.itemsOrder, overItemId, activeItemId, 'before')
    //     //         groupProps.itemsOrder = arrayDeDuplicate(groupProps.itemsOrder)
    //     //     } else {
    //     //         groupProps.itemsOrder.push(activeItemId)
    //     //         groupProps.itemsOrder = arrayDeDuplicate(groupProps.itemsOrder)
    //     //     }
    //     //     updateDefinition({groupItemsProps: definition.groupItemsProps}).then()
    //     // }
    // }, []);

    function onDragStart(event: DragStartEvent) {
        if (event.active.data.current?.type === "Column") {
            setActiveColumn(event.active.data.current.column);
            return;
        }

        if (event.active.data.current?.type === "Item") {
            setActiveItem(event.active.data.current.item);
            return;
        }
    }

    function onDragEnd(event: DragEndEvent) {
        setActiveColumn(null);
        setActiveItem(null);

        const {active, over} = event;
        if (!over) return;

        const activeId = active.id;
        const overId = over.id;

        if (activeId === overId) return;

        if (active.data.current?.type === "Column") {
            console.log("DRAG END for a column");

            const activeColumnIndex = columns.findIndex(c => c.id === activeId)
            const overColumnIndex = columns.findIndex(c => c.id === overId)

            const newGroupOrder = arrayMove(definition.groupOrder, activeColumnIndex, overColumnIndex);

            updateDefinition({groupOrder: newGroupOrder}).then()
        } else if (active.data.current?.type === "Item") {
            const activeColumnId: string = active.data.current?.columnId || ''
            const overColumnId: string = over.data.current?.columnId || over.data.current?.column?.id || ''

            const activeItemId = active.data.current?.item?.id || ''
            const overItemId = over.data.current?.item?.id || ''
            // const overColumnId = over.data.current?.columnId || ''

            // update the rowValue, update the record position in the column(remove from old column if necessary)

            const item: DataViewRow = active.data.current?.item
            if (!item) return
            if (!overColumnId) return


            if (activeColumnId !== overColumnId) {
                // let currentVal = item.record.recordValues[groupColumn.id]
                let rawVal = item.record.recordValues[groupColumn.id] as (string[] | undefined)
                const currentVal: string[] = Array.isArray(rawVal) ? rawVal : []

                let newVal: string[] = [...currentVal]
                newVal = removeAllArrayItem(newVal, activeColumnId)
                if (overColumnId && overColumnId !== UngroupedKey) {
                    newVal.push(overColumnId)
                    newVal = arrayDeDuplicate(newVal)
                }
                const values: RecordValues = {}

                values[groupColumn.id] = newVal

                updateRecordValues(database.database.id, [item.id], values)
            }

            const groupProps = definition.groupItemsProps?.[overColumnId]
            if (groupProps) {
                groupProps.itemsOrder = groupProps.itemsOrder || []
                if (overId) {
                    groupProps.itemsOrder = arrayAddElementAdjacent(groupProps.itemsOrder, overItemId, activeItemId, 'before')
                    groupProps.itemsOrder = arrayDeDuplicate(groupProps.itemsOrder)
                } else {
                    groupProps.itemsOrder.push(activeItemId)
                    groupProps.itemsOrder = arrayDeDuplicate(groupProps.itemsOrder)
                }
                updateDefinition({groupItemsProps: definition.groupItemsProps}).then()
            }


            // const valType = typeof currentVal
            // if (valType === 'undefined') {
            //     // item.record.recordValues[groupColumn.id] =
            // } else if (valType === 'string' || valType === 'number' || valType === 'boolean') {
            //     // if (['string'].includes(typeof currentVal)) {
            //
            //
            // }

            console.log({active, over, activeColumnId, overColumnId, activeItemId, overItemId})

            // I'm dropping a Item over another Item
            // setItems((items) => {
            //     const activeIndex = items.findIndex((t) => t.id === activeId);
            //     const overIndex = items.findIndex((t) => t.id === overId);
            //
            //     if (items[activeIndex].columnId != items[overIndex].columnId) {
            //         // Fix introduced after video recording
            //         items[activeIndex].columnId = items[overIndex].columnId;
            //         return arrayMove(items, activeIndex, overIndex - 1);
            //     }
            //
            //     return arrayMove(items, activeIndex, overIndex);
            // });


        }


    }

    function onDragOver(event: DragOverEvent) {
        /**
         const {active, over} = event;
         if (!over) return;

         const activeId = active.id;
         const overId = over.id;

         if (activeId === overId) return;

         const isActiveItem = active.data.current?.type === "Item";
         const isOverItem = over.data.current?.type === "Item";

         const activeColumnId = active.data.current?.columnId || ''
         const overColumnId = over.data.current?.columnId || ''

         if (!isActiveItem) return;

         console.log({active, over, isActiveItem, isOverItem, activeColumnId, overColumnId})

         // I'm dropping a Item over another Item
         if (isActiveItem && isOverItem) {
         // setItems((items) => {
         //     const activeIndex = items.findIndex((t) => t.id === activeId);
         //     const overIndex = items.findIndex((t) => t.id === overId);
         //
         //     if (items[activeIndex].columnId != items[overIndex].columnId) {
         //         // Fix introduced after video recording
         //         items[activeIndex].columnId = items[overIndex].columnId;
         //         return arrayMove(items, activeIndex, overIndex - 1);
         //     }
         //
         //     return arrayMove(items, activeIndex, overIndex);
         // });
         }
         //
         // const isOverAColumn = over.data.current?.type === "Column";
         //
         // if (isActiveAItem && isOverAColumn) {
         //     setItems((items) => {
         //         const activeIndex = items.findIndex((t) => t.id === activeId);
         //
         //         items[activeIndex].columnId = overId;
         //         console.log("DROPPING ITEM OVER COLUMN", {activeIndex});
         //         return arrayMove(items, activeIndex, activeIndex);
         //     });
         // }
         */
    }

    return <>
        <div className="size-full flex flex-col overflow-hidden">
            {definition.lockContent && !shared && <>
                <ContentLocked/>
            </>}
            <div className='flex-1 overflow-hidden select-none'>
                <DndContext
                    autoScroll={{acceleration: 0.5, threshold: {x: 25, y: 10}}}
                    sensors={sensors}
                    onDragStart={onDragStart}
                    onDragEnd={onDragEnd}
                    onDragOver={onDragOver}>
                    <div className="w-full h-full overflow-x-auto overflow-y-hidden gap-4 p-4 pt-2 pb-0 flex">
                        <SortableContext items={columnsId}>
                            {columns.map(col => {
                                    return <BoardColumn
                                        column={col}
                                        key={col.id}
                                        // @ts-ignore
                                        columnProps={definition.groupItemsProps[col.id]}
                                        items={boardItems.boardItems[col.id].map(id => boardItems.rowIdsMap[id])}
                                        // deleteColumn={deleteColumn}
                                        updateColumn={updateColumn}
                                        canEditStructure={canEditStructure}
                                        canEditData={canEditData}
                                        databaseDefinition={database.database.definition}
                                        dataColumnPropsMap={definition.columnPropsMap}
                                        dataColumnsOrder={definition.columnsOrder}
                                        titleColId={titleColId}
                                        dndDisabled={!editable}
                                        addRecord={handleAddRecord}
                                        databaseId={database.database.id}
                                    />
                                }
                            )}
                        </SortableContext>
                        {canEditStructure && <NewColumn createColumn={createNewColumn}/>}
                        {canEditStructure && <HiddenColumns
                            boardItems={boardItems.boardItems}
                            columns={hiddenColumns}
                            groupItemsProps={definition.groupItemsProps}
                            rowIdsMap={boardItems.rowIdsMap}
                            updateColumn={updateColumn}
                            databaseDefinition={database.database.definition}
                            dataColumnPropsMap={definition.columnPropsMap}
                            dataColumnsOrder={definition.columnsOrder}
                            disabled={!editable}
                            titleColId={titleColId}
                        />}
                    </div>

                    {createPortal(
                        <DragOverlay>
                            {activeColumn && (
                                <BoardColumn
                                    column={activeColumn}
                                    key={activeColumn.id}
                                    // @ts-ignore
                                    columnProps={definition.groupItemsProps[activeColumn.id]}
                                    items={boardItems.boardItems[activeColumn.id].map(id => boardItems.rowIdsMap[id])}
                                    // deleteColumn={deleteColumn}
                                    updateColumn={updateColumn}
                                    databaseDefinition={database.database.definition}
                                    dataColumnPropsMap={definition.columnPropsMap}
                                    dataColumnsOrder={definition.columnsOrder}
                                    titleColId={titleColId}
                                    dndDisabled={!editable}
                                    databaseId={database.database.id}
                                />
                            )}
                            {activeItem && (
                                <ItemCard
                                    dataColumnPropsMap={definition.columnPropsMap}
                                    dataColumnsOrder={definition.columnsOrder}
                                    databaseDefinition={database.database.definition}
                                    titleColId={titleColId}
                                    key={activeItem.id}
                                    columnId=''
                                    databaseId={database.database.id}
                                    item={activeItem}/>
                            )}
                        </DragOverlay>,
                        document.body
                    )}
                </DndContext>
            </div>
            
            {/* AddRecordModal for intelligent board record creation */}
            {showAddModal && (
                <AddRecordModal
                    open={showAddModal}
                    onClose={() => setShowAddModal(false)}
                    databaseId={database.database.id}
                    viewFilter={getGroupFilter()}
                    contextualFilter={filter}
                    onRecordCreated={handleRecordCreated}
                />
            )}
        </div>
    </>
}