"use client";

import React from 'react';
import { usePeekStack, PeekItem, PeekStackProvider } from '@/providers/peekStack';
import { useWorkspace } from '@/providers/workspace';
import { useMaybeTemplate } from '@/providers/template';
import { useMaybeShared } from '@/providers/shared';
import { Sheet, SheetClose, SheetContent, SheetDescription, She<PERSON><PERSON>ooter, SheetHeader, SheetT<PERSON>le, SheetTrigger } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { ArrowUpRightAndArrowDownLeftFromCenterIcon } from "@/components/icons/FontAwesomeRegular";
import { RecordTabViewsProvider } from "@/providers/recordTabViews";
import { RecordProvider } from "@/providers/record";
import { RecordExtras } from "@/components/workspace/main/record/components/recordExtras";
import { membersToPersons } from "@/components/workspace/main/views/table/renderer/fields/person";
import { LinkedDatabases, transformRawRecords } from "opendb-app-db-utils/lib/utils/db";
import { DatabaseFieldDataType, LinkedColumn, ProcessedDbRecord } from "opendb-app-db-utils/lib/typings/db";
import { PageProvider } from "@/providers/page";

interface StackedPeekRecordProps {
    canEdit?: boolean
}

export const StackedPeekRecord = ({ canEdit = true }: StackedPeekRecordProps) => {
    const { peekStack, popPeek } = usePeekStack();
    const { databaseStore, databasePageStore, members, url } = useWorkspace();
    const template = useMaybeTemplate();
    const shared = useMaybeShared();

    const canExpand = !shared && !template;

    console.log('🏗️ StackedPeekRecord render:', {
        peekStackLength: peekStack.length,
        peekStack: peekStack.map(item => ({
            id: item.id,
            recordId: item.recordId,
            databaseId: item.databaseId,
            level: item.level
        })),
        timestamp: new Date().toISOString()
    });

    if (peekStack.length === 0) return null;

    return (
        <>
            {peekStack.map((peekItem, index) => (
                <PeekRecordLevel
                    key={peekItem.id}
                    peekItem={peekItem}
                    canEdit={canEdit}
                    canExpand={canExpand}
                    onClose={() => popPeek(peekItem.id)}
                    databaseStore={databaseStore}
                    databasePageStore={databasePageStore}
                    members={members}
                    url={url}
                />
            ))}
        </>
    );
};

interface PeekRecordLevelProps {
    peekItem: PeekItem;
    canEdit: boolean;
    canExpand: boolean;
    onClose: () => void;
    databaseStore: any;
    databasePageStore: any;
    members: any;
    url: (path: string) => string;
}

const PeekRecordLevel = ({
    peekItem,
    canEdit,
    canExpand,
    onClose,
    databaseStore,
    databasePageStore,
    members,
    url
}: PeekRecordLevelProps) => {
    const database = databaseStore[peekItem.databaseId];
    const record = databaseStore[peekItem.databaseId]?.recordsIdMap?.[peekItem.recordId];
    const databasePage = databasePageStore?.[peekItem.databaseId];

    console.log('🎭 PeekRecordLevel render:', {
        peekItem,
        hasDatabase: !!database,
        hasRecord: !!record,
        hasDatabasePage: !!databasePage,
        recordId: peekItem.recordId,
        databaseId: peekItem.databaseId,
        level: peekItem.level,
        recordData: record ? {
            id: record.record.id,
            databaseId: record.record.databaseId,
            title: record.record.title
        } : null,
        timestamp: new Date().toISOString()
    });

    const onOpenChange = (open: boolean) => {
        if (!open) {
            onClose();
        }
    };

    if (!database || !record || !databasePage) return null;

    const recordInfo = record;

    let processedRecord: ProcessedDbRecord | null = null;
    const persons = membersToPersons(members);

    const linkedDatabaseId = Object.values(database.database.definition.columnsMap)
        .filter((c: any) => c.type === DatabaseFieldDataType.Linked && c.databaseId)
        .map((c: any) => (c as LinkedColumn).databaseId);

    const linkedDatabases: LinkedDatabases = {};

    for (const id of linkedDatabaseId) {
        const db = databaseStore[id];
        if (db) {
            linkedDatabases[id] = {
                id,
                definition: db.database.definition,
                recordsMap: {},
                srcPackageName: db.database.srcPackageName
            };
            for (let r of Object.values(db.recordsIdMap)) {
                linkedDatabases[id].recordsMap[(r as any).record.id] = (r as any).record;
            }
        }
    }

    const records = [recordInfo.record];
    const processedRecords = transformRawRecords(
        database.database.definition,
        records,
        persons,
        linkedDatabases
    );
    processedRecord = processedRecords[0];

    const href = url(`/databases/${record.record.databaseId}/records/${record.record.id}`);

    // Calculate z-index based on level to ensure proper stacking
    const baseZIndex = 50;
    const zIndex = baseZIndex + peekItem.level * 10;
    
    
    const baseWidth = 50;
    const widthReduction = Math.min(peekItem.level * 5, 20); 
    const width = baseWidth - widthReduction;

    return (
        <Sheet defaultOpen={true} onOpenChange={onOpenChange}>
            <SheetTrigger asChild>
                <div />
            </SheetTrigger>
            <SheetContent 
                className='!w-[50vw] !min-w-[400px] !max-w-full bg-white p-0 pt-8' 
                style={{ 
                    width: `${width}vw`,
                    minWidth: '400px',
                    maxWidth: '100%',
                    zIndex: zIndex
                }}
            >
                {canExpand && (
                    <Button variant='ghost' asChild className='absolute right-12 top-2.5 !size-6 !p-1.5 rounded-full'>
                        <Link href={href}>
                            <ArrowUpRightAndArrowDownLeftFromCenterIcon className='size-full'/>
                        </Link>
                    </Button>
                )}
                <div className='size-full flex flex-col overflow-hidden'>
                    <SheetHeader className='hidden'>
                        <SheetTitle className='font-bold text-base'>Peek Record</SheetTitle>
                        <SheetDescription className='hidden'>
                            Make changes to your record here. Click save when you're done.
                        </SheetDescription>
                    </SheetHeader>
                    <div className='flex-1 overflow-hidden'>
                        <PageProvider permissiblePage={databasePage} id={databasePage.page.id}>
                            <RecordTabViewsProvider>
                                <RecordProvider
                                    recordInfo={{...record, processedRecord}}
                                    database={database.database}
                                >
                                    {/*<RecordOverview isTabbed/>*/}
                                    <RecordExtras showOverview/>
                                    {/*<RecordDetail />*/}
                                </RecordProvider>
                            </RecordTabViewsProvider>
                        </PageProvider>
                    </div>
                    <SheetFooter className='hidden'>
                        <SheetClose asChild>
                            <Button type="submit" className='rounded-full'>Save</Button>
                        </SheetClose>
                    </SheetFooter>
                </div>
            </SheetContent>
        </Sheet>
    );
}; 