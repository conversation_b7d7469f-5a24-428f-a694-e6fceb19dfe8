"use client"

import React, {PropsWithChildren, useEffect} from "react";
import "./baseLayout.css"
import {Sidebar} from "@/components/workspace/main/common/sidebar";
import {useScreenSize} from "@/providers/screenSize";
import LimitedFunctionalityView from "./views/common/LimitedFunctionalityView";
import { useWorkspace } from "@/providers/workspace";
import { QuotaExceededContentWrap } from "./views/common/quotaExceededContentWrap";
import { usePathname } from "next/navigation";
import { StackedPeekRecord } from "./views/StackedPeekRecord";


interface BaseLayoutProps {
    showSidebar?: boolean
}

export const BaseLayout = (props: PropsWithChildren<BaseLayoutProps>) => {
    const { isCollapsed, setCollapsed, isMobile } = useScreenSize()
    const { workspace } = useWorkspace()

    const pathname = usePathname()
    const isSettingsPage = pathname.includes(`/${workspace.workspace.domain}/settings`)


    useEffect(() => {
        if (isMobile && !isCollapsed) {
            setCollapsed(true)
        }
    }, [isMobile]);
    return <>
        <div className={isCollapsed ? 'collapsed' : ''} id="app-wrap">
            {props.showSidebar && <Sidebar {...{collapse: isCollapsed, setCollapse: setCollapsed}}/>}
            {isMobile && !isCollapsed && <div className='fixed size-full z-50 bg-black opacity-70' onClick={() => setCollapsed(true)}/>}
            <Main>{(workspace.workspace.isFunctionalityLimited && props.showSidebar) ? <LimitedFunctionalityView /> :
                <>
                    {!isSettingsPage && <QuotaExceededContentWrap />}
                    {props.children}
                    {/* Stacked Peek Records - render over everything */}
                    <StackedPeekRecord />
                </>

            }</Main>
        </div>
    </>
}

const Main = (props: PropsWithChildren) => {
    return <>
        <main className="h-full flex-1 overflow-hidden">
            {props.children}
        </main>
    </>
}