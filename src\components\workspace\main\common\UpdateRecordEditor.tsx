"use client";

import Re<PERSON>, { use<PERSON>emo, useState } from "react";
import { useWorkspace } from "@/providers/workspace";
import { DatabaseColumn, DatabaseFieldDataType, DatabaseColumnDbValue } from "opendb-app-db-utils/lib/typings/db";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { TrashIcon, PlusIcon } from "@heroicons/react/24/outline";
import { MentionInfo } from "@/components/custom-ui/mentionInput";
import { FormFieldAreaProps } from "@/components/workspace/main/views/form/components/element/text";
import { FormColumnCustomization } from "opendb-app-db-utils/lib/typings/view";
import { RecordValues } from "opendb-app-db-utils/lib/typings/db";
import { DatabaseColumnSelect } from "@/components/workspace/main/common/databaseColumnSelect";
import { AIFieldArea } from "@/components/workspace/main/views/form/components/element/ai";
import { TextField<PERSON><PERSON> } from "@/components/workspace/main/views/form/components/element/text";
import { CheckboxFieldArea } from "@/components/workspace/main/views/form/components/element/checkbox";
import { SelectFieldArea } from "@/components/workspace/main/views/form/components/element/select";
import { PersonFieldArea } from "@/components/workspace/main/views/form/components/element/person";
import { LinkedFieldArea } from "@/components/workspace/main/views/form/components/element/linked";
import { DateFieldArea } from "@/components/workspace/main/views/form/components/element/date";
import { DateRenderColumn, PersonRenderColumn, TextRenderColumns } from "@/components/workspace/main/views/form/components/common/formFieldBody";
import { MentionInput } from "@/components/custom-ui/mentionInput";

interface Update {
    columnId: string;
    value: unknown;
}

interface UpdateRecordEditorProps {
    databaseId: string;
    updates: Update[];
    onUpdate: (updates: Update[]) => void;
    variableKeyMap: Record<string, MentionInfo>;
}

const CustomFieldRenderer = ({ column, fieldProps, variableKeyMap }: { 
    column: DatabaseColumn | undefined; 
    fieldProps: FormFieldAreaProps;
    variableKeyMap: Record<string, MentionInfo>;
}) => {
    if (!column) return null;
    
    if (TextRenderColumns.includes(column.type)) {
        const value = fieldProps.values[column.id];
        const stringValue = typeof value === 'string' ? value : (value ? String(value) : '');

    return (
            <MentionInput
                keyMap={variableKeyMap}
                value={stringValue}
                onChange={(value) => {
                    const cv: RecordValues = {};
                    cv[column.id] = value;
                    fieldProps.updateValues(cv);
                }}
                placeholder={`Enter value for ${column.title} (use @ for variables)`}
                className="text-xs p-2 border rounded min-h-8 w-full"
                keepNewLine={column.type === DatabaseFieldDataType.Text && column.isLong}
        />
    );
}

    return (
        <>
            {column.type === DatabaseFieldDataType.AI && <AIFieldArea {...fieldProps} />}
            {column.type === DatabaseFieldDataType.Checkbox && <CheckboxFieldArea {...fieldProps} />}
            {column.type === DatabaseFieldDataType.Select && <SelectFieldArea {...fieldProps} />}
            {PersonRenderColumn.includes(column.type) && <PersonFieldArea {...fieldProps} />}
            {column.type === DatabaseFieldDataType.Linked && <LinkedFieldArea {...fieldProps} />}
            {DateRenderColumn.includes(column.type) && <DateFieldArea {...fieldProps} />}
        </>
    );
};

export const UpdateRecordEditor = ({ databaseId, updates, onUpdate, variableKeyMap }: UpdateRecordEditorProps) => {
    const { databaseStore } = useWorkspace();
    const database = databaseStore[databaseId];
    
    const updatableColumns = useMemo(() => {
        if (!database) return [];
        
        const nonUpdatableTypes = [
            DatabaseFieldDataType.AI,                   
            DatabaseFieldDataType.ButtonGroup,     
            DatabaseFieldDataType.ScannableCode,   
            DatabaseFieldDataType.CreatedBy,       
            DatabaseFieldDataType.UpdatedBy,
            DatabaseFieldDataType.CreatedAt,
            DatabaseFieldDataType.UpdatedAt,
            DatabaseFieldDataType.Summarize,       
            DatabaseFieldDataType.Derived,
            DatabaseFieldDataType.UUID,
            DatabaseFieldDataType.Files,
        ];

        return Object.values(database.database.definition.columnsMap)
            .filter(col => !nonUpdatableTypes.includes(col.type) && col.id !== 'id');
    }, [database]);

    if (!database) {
        return <p className="text-xs text-red-500">Selected database not found.</p>;
    }

    const handleUpdate = (index: number, newUpdate: Partial<Update>) => {
        const newUpdates = [...updates];
        const oldUpdate = newUpdates[index];
        newUpdates[index] = { ...oldUpdate, ...newUpdate };

        if ('columnId' in newUpdate && oldUpdate.columnId !== newUpdate.columnId) {
            const newColumn = updatableColumns.find(c => c.id === newUpdate.columnId);
            if (newColumn) {
                if ([DatabaseFieldDataType.Select, DatabaseFieldDataType.Linked, DatabaseFieldDataType.Person].includes(newColumn.type) && (newColumn as DatabaseColumn & { isMulti?: boolean }).isMulti) {
                    newUpdates[index].value = [];
                } else {
                    newUpdates[index].value = '';
                }
            }
        }
        
        onUpdate(newUpdates);
    };

    const addUpdate = () => {
        onUpdate([...updates, { columnId: '', value: '' }]);
    };

    const deleteUpdate = (index: number) => {
        const newUpdates = [...updates];
        newUpdates.splice(index, 1);
        onUpdate(newUpdates);
    };

    const columnIds = updates.map(update => update.columnId).filter(Boolean);

    return (
        <div className="space-y-2">
            <div className='flex flex-col gap-2'>
            {updates.map((update, index) => {
                    const selected = update.columnId ? [update.columnId] : [];
                    const column = selected.length > 0 ? database.database.definition.columnsMap[update.columnId] : undefined;

                    const recordValues: RecordValues = {};
                    if (update.columnId && update.value !== undefined) {
                        recordValues[update.columnId] = update.value as DatabaseColumnDbValue;
                    }

                    const fieldProps: FormFieldAreaProps = {
                        databaseId: databaseId,
                        columnProps: {
                            allowMultiple: column && column.type === DatabaseFieldDataType.Select ? true : false,
                            isHidden: false,
                            isRequired: false
                        },
                        columnsMap: database.database.definition.columnsMap,
                        id: update.columnId,
                        isEditing: false,
                        updateFieldProps: (id: string, update: Partial<FormColumnCustomization>) => {
                            // This is a no-op for the update record editor
                        },
                        updateValues: (u: RecordValues) => {
                            const newValue = u[update.columnId];
                            handleUpdate(index, { value: newValue });
                        },
                        values: recordValues,
                        setActiveField: (id: string) => {
                            // This is a no-op for the update record editor
                        },
                        activeField: ''
                    };

                return (
                        <div className='flex gap-2 items-center' key={index}>
                            <div className='flex-1 max-w-64 flex items-center'>
                                <DatabaseColumnSelect 
                                    databaseId={databaseId}
                                    selected={selected}
                                    filterFn={(c: DatabaseColumn) => {
                                        const isUpdatable = updatableColumns.some(updatableCol => updatableCol.id === c.id);
                                        const isNotSelected = c.id === column?.id || !columnIds.includes(c.id);
                                        return isUpdatable && isNotSelected;
                                    }}
                                    onChange={(selected1: string[]) => {
                                        handleUpdate(index, { columnId: selected1[0], value: undefined });
                                    }}
                                />
                            </div>
                            <div className='flex-1 flex items-center pt-1'>
                                {column && (
                                    <CustomFieldRenderer 
                                        column={column} 
                                        fieldProps={fieldProps} 
                                        variableKeyMap={variableKeyMap}
                                    />
                                )}
                            </div>
                            <Button 
                                variant="ghost" 
                                size="sm" 
                                onClick={() => deleteUpdate(index)} 
                                className="p-1 h-6 w-6 text-gray-700 hover:bg-gray-100"
                            >
                            <TrashIcon className="h-3 w-3" />
                        </Button>
                    </div>
                );
            })}
            </div>
            <div className="flex">
                <Button
                    variant="ghost"
                    onClick={addUpdate}
                    className="mr-2 text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1">
                    Add field
            </Button>
                <div className='flex-1'></div>
            </div>
        </div>
    );
}; 