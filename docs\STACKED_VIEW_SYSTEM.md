# Stacked View System Documentation

## Overview

The Stacked View System in OpenDashboard allows users to open multiple record previews (peeks) in a layered, stackable interface. This system provides a sophisticated way to navigate between related records while maintaining context and allowing for deep exploration of data relationships.

## Core Components

### 1. PeekStack Provider (`src/providers/peekStack.tsx`)

The central state management for the stacked peek system.

**Key Features:**
- Manages a stack of peek items with unique IDs
- Each peek item contains: `id`, `recordId`, `databaseId`, and `level`
- Provides methods to push, pop, and clear peeks
- Tracks the current peek context

**Main Methods:**
- `pushPeek(recordId, databaseId)`: Adds a new peek to the stack
- `popPeek(id?)`: Removes the top peek or a specific peek by ID
- `clearPeekStack()`: Removes all peeks
- `getTopPeek()`: Returns the current top peek
- `isInPeekContext`: Boolean indicating if any peeks are active

### 2. useStackedPeek Hook (`src/hooks/useStackedPeek.tsx`)

An intelligent hook that determines whether to use stacked peeks or regular peeks based on context.

**Logic:**
- If already in a record context (regular peek or stacked peek), it stacks a new peek
- If in a regular view context, it uses the normal peek functionality
- Provides `openRecord(recordId, databaseId)` method for consistent record opening

### 3. StackedPeekRecord Component (`src/components/workspace/main/views/StackedPeekRecord.tsx`)

The main rendering component that displays all stacked peeks.

**Features:**
- Renders multiple peek levels with decreasing width and increasing z-index
- Each level shows a complete record view with RecordExtras
- Provides expand button to open full record view
- Handles proper stacking order and visual hierarchy

**Visual Stacking:**
- Base z-index: 50
- Each level adds 10 to z-index
- Width decreases by 5% per level (minimum 30%)
- Proper layering ensures top peeks are always visible

### 4. RecordTabViews Provider (`src/providers/recordTabViews.tsx`)

Manages tab state for record views within the stacked peek system.

**State Management:**
- Filter conditions
- Sort configurations  
- Search terms
- Clear state functionality

## How It Works

### 1. Context Detection

The system intelligently detects the current context:

```typescript
// From useStackedPeek.tsx
const inRecordContext = !!maybeRecord || isInPeekContext;

if (inRecordContext) {
    // Stack a new peek
    pushPeek(recordId, databaseId);
} else {
    // Use regular peek
    setPeekRecordId(recordId);
}
```

### 2. Stack Management

When a user clicks on a record link within a peek:

1. **Context Check**: System determines if already in a record context
2. **Stack Push**: If in context, pushes new peek to stack
3. **Visual Rendering**: New peek appears with proper z-index and width
4. **Navigation**: User can navigate between stacked records

### 3. Visual Hierarchy

The stacking system creates a visual hierarchy:

```
Level 0: 50vw width, z-index 50
Level 1: 45vw width, z-index 60  
Level 2: 40vw width, z-index 70
...and so on
```

### 4. Integration Points

**BaseLayout Integration:**
```typescript
// From src/components/workspace/main/baseLayout.tsx
<StackedPeekRecord />
```

**Button Actions:**
```typescript
// From src/utils/buttonAction.ts
if (services.pushPeek) {
    services.pushPeek(recordId, databaseId);
} else if (services.setPeekRecord) {
    services.setPeekRecord(databaseId, recordId);
}
```

**Calendar View:**
```typescript
// From src/components/workspace/main/views/calendar/index.tsx
const { openRecord } = useStackedPeek();
```

## Usage Patterns

### 1. Opening Records from Views

```typescript
const { openRecord } = useStackedPeek();

// In a table, board, or list view
const handleRecordClick = (recordId: string) => {
    openRecord(recordId, databaseId);
};
```

### 2. Navigation Within Peeks

When a user clicks on a linked record within a peek:
- The system detects the record context
- Pushes a new peek to the stack
- Maintains the relationship chain

### 3. Closing Peeks

Users can close peeks in multiple ways:
- Click the X button on individual peeks
- Use the expand button to open full record view
- Navigate away from the context

## Technical Implementation Details

### State Management

The peek stack is managed through React Context:

```typescript
interface PeekItem {
    id: string;
    recordId: string;
    databaseId: string;
    level: number;
}
```

### Performance Considerations

- Each peek level renders a complete record view
- Proper memoization prevents unnecessary re-renders
- Z-index management ensures proper layering
- Width calculations maintain visual hierarchy

### Error Handling

- Graceful fallback to regular peek if stack unavailable
- Proper cleanup when peeks are closed
- Error boundaries for individual peek components

## Benefits

1. **Context Preservation**: Users can explore related records without losing their place
2. **Deep Navigation**: Allows for complex multi-level record exploration
3. **Visual Clarity**: Clear hierarchy shows the relationship chain
4. **Flexible Interaction**: Multiple ways to navigate and close peeks
5. **Performance**: Efficient rendering and state management

## Future Enhancements

Potential improvements could include:
- Breadcrumb navigation showing the peek stack
- Keyboard shortcuts for peek navigation
- Drag and drop reordering of peeks
- Peek history and back/forward navigation
- Custom peek layouts and configurations

## Troubleshooting

### Common Issues

1. **Infinite Loops**: Ensure tab arrays aren't recreated on every render
2. **Z-index Conflicts**: Verify proper z-index calculation and layering
3. **State Synchronization**: Check that peek stack state is properly managed
4. **Component Interference**: Ensure RecordExtras instances don't interfere with each other

### Debugging Tips

- Check the peek stack state in React DevTools
- Verify context detection logic
- Monitor z-index and width calculations
- Ensure proper cleanup on component unmount 