"use client"


import {create<PERSON><PERSON><PERSON><PERSON>, Props<PERSON><PERSON><PERSON><PERSON><PERSON>n, use<PERSON><PERSON>back, useContext, useRef, useState} from "react";
import {SocketTimeoutMS, useWorkspaceSocket} from "@/providers/workspaceSocket";
import {AIColumnRecordMetadata, DatabaseColumn, DatabaseFieldDataType, DbRecordFilter, DbRecordSort, FileItem, FilesColumnDbValue, Match, RecordValues} from "opendb-app-db-utils/lib/typings/db";
import {SocketCallbackData, WorkspaceHandlerEvent} from "@/typings/socket";
import {BackendAPIResponse, defaultAPIMessage, UploadCallback} from "@/api/common";
import {useAlert} from "@/providers/alert";
import {UploadItem} from "@/components/workspace/main/views/table/renderer/fields/files";
import {useWorkspace} from "@/providers/workspace";
import {useAuth} from "@/providers/user";
import {uploadFile} from "@/api/workspace";
import {KeyValueStore, WorkspaceUpload} from "@/typings/workspace";
import {generateUUID} from "opendb-app-db-utils/lib/methods/string";
import 'react-photo-view/dist/react-photo-view.css';
import {Activity, ActivityObjectType, AdjacentDatabases, Database, OnDuplicateAction, PermissibleDatabaseWithPermissions, Record, RecordMetaKey} from "@/typings/database";
import {CacheMethods, useCache} from "@/providers/cache";
import {DatabaseConstants} from "@/components/workspace/main/views/table/renderer/common/addColumn";
import {DashboardTransaction, ViewDefinition, ViewType} from "opendb-app-db-utils/lib/typings/view";
import {AccessLevel, Page, PagePermission, View} from "@/typings/page";
import {usePreview} from "@/providers/preview";
import {useParams} from "next/navigation";
import {RecordsIdMap} from "@/typings/utilities";
import {removeAllArrayItem} from "opendb-app-db-utils/lib";
import {RecordTabViewsContext} from "@/providers/recordTabViews";

export interface ViewsProviderProps {

}


interface RecordQueue {

}

export interface FileUploadQueue {
    [databaseId: string]: {
        [recordId: string]: {
            [columnId: string]: {
                [itemId: string]: UploadItem
            }
        }
    }
}


export const ViewsProvider = (props: PropsWithChildren<ViewsProviderProps>) => {
    const [selectedIds, setSelectedIds] = useState<string[]>([])
    // const [currentViewId, setCurrentViewId] = useState<string>(props.defaultViewId)

    const params = useParams()
    const refViewId = (params.viewId || '') as string
    const refPageId = (params.pageId || '') as string


    const {toast, confirm, promptUpgrade} = useAlert()
    const {token} = useAuth()
    const {workspace, databaseStore, updateDatabasePageStore, databasePageStore, addDatabase, updateDatabaseStore, addAdjacentDatabases, databaseErrorStore, updateDatabaseErrorState} = useWorkspace()
    const {socket, isConnected} = useWorkspaceSocket()
    const {previewFiles} = usePreview()
    const [files, setFiles] = useState<FileItem[]>()
    const [index, setIndex] = useState(0)
    const [filter, setFilter] = useState<DbRecordFilter>({match: Match.All, conditions: []})
    const [sorts, setSorts] = useState<DbRecordSort[]>([])
    const [search, setSearch] = useState('')
    const [peekRecordId, setPeekRecordId] = useState('')
    const [peekDatabaseId, setPeekDatabaseId] = useState('')

    const setPeekRecord = (databaseId: string, recordId: string) => {
        // Always set both database and record IDs
        setPeekDatabaseId(databaseId)
        setPeekRecordId(recordId)
    };

    const cache = useCache()

    const [, setRenderCount] = useState(0);

    const forceRender = useCallback(() => {
            setRenderCount(prevCount => prevCount + 1);
            // console.log("Force Render:")
        },
        [] // Note: No dependencies needed here
    );

    const uploadQueueRef = useRef<FileUploadQueue>({})

    const onSocketError = (error: string) => {
        toast.error(error)
    }

    const updateRecordValues = async (databaseId: string, recordIds: string[], values: RecordValues, meta: KeyValueStore = {}) => {
        // , refPageId?:string, refViewId?:string
        if (!socket || !isConnected) {
            onSocketError(defaultAPIMessage())
            return
        }

        const res: SocketCallbackData<{}> = await socket.timeout(5000).emitWithAck(WorkspaceHandlerEvent.UpdateCellValues, {
            databaseId,
            recordIds,
            values,
            meta,
            refPageId,
            refViewId
        })
        console.log("Update cell values:", res)
        if (res.status !== 'ok') {
            const error = res.message || defaultAPIMessage()
            onSocketError(error)
            return
        }
        return true
    }

    const updateDatabaseColumn = async (databaseId: string, columnId: string, type: DatabaseFieldDataType, column: Partial<DatabaseColumn>) => {
        if (!socket || !isConnected) {
            onSocketError(defaultAPIMessage())
            return
        }

        const res: SocketCallbackData<{}> = await socket.timeout(SocketTimeoutMS).emitWithAck(WorkspaceHandlerEvent.UpdateDatabaseColumn, {
            databaseId: databaseId,
            columnId: columnId,
            update: {...column, type},
            refPageId,
            refViewId
        })
        // console.log("Update database column res:", {res, column})
        if (res.status !== 'ok') {
            const error = res.message || defaultAPIMessage()
            toast.error(error)
            return
        }
        return true
    }

    const updateDatabaseTitleColumn = async (databaseId: string, columnId: string) => {
        if (!socket || !isConnected) {
            onSocketError(defaultAPIMessage())
            return
        }

        const res: SocketCallbackData<{}> = await socket.timeout(SocketTimeoutMS).emitWithAck(WorkspaceHandlerEvent.UpdateTitleColumn, {
            databaseId: databaseId,
            columnId: columnId,
            refPageId,
            refViewId
        })
        if (res.status !== 'ok') {
            const error = res.message || defaultAPIMessage()
            toast.error(error)
            return
        }
    }

    const uploadWorkspaceFile = (namespace: string, seed: string, uploadKey: string, file: File, onComplete: (res: BackendAPIResponse<{
        upload: WorkspaceUpload
    }>) => void) => {
        if (!token) {
            toast.error(defaultAPIMessage())
            return
        }
        const doInit = () => {
            uploadQueueRef.current[namespace] = uploadQueueRef.current[namespace] || {}
            uploadQueueRef.current[namespace][seed] = uploadQueueRef.current[namespace][seed] || {}
            uploadQueueRef.current[namespace][seed][uploadKey] = uploadQueueRef.current[namespace][seed][uploadKey] || {}

            return uploadQueueRef.current[namespace][seed][uploadKey]
        }
        const id = generateUUID()

        const item: UploadItem = {
            id: id,
            progress: 0
        }
        const callback: UploadCallback = {
            onStart(): void {
                const queue = doInit()
                queue[id] = item
                forceRender()
            },
            onComplete: (res: BackendAPIResponse<{
                upload: WorkspaceUpload
            }>) => {
                const queue = doInit()
                if (queue[id]) delete queue[id]
                if (!res.isSuccess) {
                    const error = res.error || defaultAPIMessage()
                    toast.error(error)
                    forceRender();
                    return
                }
                // const upload = res.data.data.upload
                // const fileItem: FileItem = {
                //     id: String(upload.id), link: upload.finalUrl, name: upload.name, type: upload.type
                // }
                // if (databaseStore[databaseId] && databaseStore[databaseId].recordsIdMap[recordId]) {
                //     const oldValues = databaseStore[databaseId].recordsIdMap[recordId].record.recordValues
                //     let oldValue = oldValues[columnId] as (FilesColumnDbValue | undefined | null)
                //     if (!oldValue || !Array.isArray(oldValue)) oldValue = []
                //     oldValue.push(fileItem)
                //
                //     const values: RecordValues = {}
                //     values[columnId] = oldValue
                //
                //     updateRecordValues(databaseId, recordId, values)
                // }

                forceRender();

                onComplete(res)
            },
            onProgress: percent => {
                // setUProgress(percent)
                const queue = doInit()
                if (!queue[id]) return

                queue[id].progress = percent
                forceRender()
            }
        }
        uploadFile(token.token, workspace.workspace.id, file, callback)
    }

    const uploadFileToColumn = async (databaseId: string, recordId: string, columnId: string, file: File) => {
        if (!token) return
        if (!databaseStore[databaseId]) return
        if (!databaseStore[databaseId].recordsIdMap[recordId]) return

        const doInit = () => {
            uploadQueueRef.current[databaseId] = uploadQueueRef.current[databaseId] || {}
            uploadQueueRef.current[databaseId][recordId] = uploadQueueRef.current[databaseId][recordId] || {}
            uploadQueueRef.current[databaseId][recordId][columnId] = uploadQueueRef.current[databaseId][recordId][columnId] || {}

            return uploadQueueRef.current[databaseId][recordId][columnId]
        }

        const id = generateUUID()

        const item: UploadItem = {
            id: id,
            progress: 0
        }
        const callback: UploadCallback = {
            onStart(): void {
                const queue = doInit()
                queue[id] = item
                forceRender()
            },
            onComplete: (res: BackendAPIResponse<{
                upload: WorkspaceUpload
            }>) => {
                const queue = doInit()
                if (queue[id]) delete queue[id]
                if (!res.isSuccess) {
                    const error = res.error || defaultAPIMessage()
                    toast.error(error)
                    forceRender();
                    return
                }
                const upload = res.data.data.upload
                const fileItem: FileItem = {
                    id: String(upload.id), link: upload.finalUrl, name: upload.name, type: upload.type, size: upload.size
                }
                if (databaseStore[databaseId] && databaseStore[databaseId].recordsIdMap[recordId]) {
                    const oldValues = databaseStore[databaseId].recordsIdMap[recordId].record.recordValues
                    let oldValue = oldValues[columnId] as (FilesColumnDbValue | undefined | null)
                    if (!oldValue || !Array.isArray(oldValue)) oldValue = []
                    oldValue.push(fileItem)

                    const values: RecordValues = {}
                    values[columnId] = oldValue

                    updateRecordValues(databaseId, [recordId], values)
                }

                forceRender();
            },
            onProgress: percent => {
                // setUProgress(percent)
                const queue = doInit()
                if (!queue[id]) return

                queue[id].progress = percent
                forceRender()
            }
        }
        uploadFile(token.token, workspace.workspace.id, file, callback)
    }

    const createRecords = async (databaseId: string, values: RecordValues[], meta?: KeyValueStore, onDuplicate = OnDuplicateAction.Update) => {
        if (!socket || !isConnected) {
            onSocketError(defaultAPIMessage())
            return
        }
        setSelectedIds([])
        const res: SocketCallbackData<{
            records: Record[]
        }> = await socket.timeout(10000).emitWithAck(WorkspaceHandlerEvent.AddDatabaseRecords, {
            databaseId,
            recordsData: {
                valuesList: values,
                onDuplicate
            },
            refPageId,
            refViewId
        })
        console.log("Create record:", res)
        if (res.status !== 'ok') {
            const error = res.message || defaultAPIMessage()
            toast.error(error)
            return
        }
        cache.setCache(DatabaseConstants.NewlyCreatedRecordsKey, res.data.records.map(r => r.id), 30)
        console.log("Record key saved in cache:", res.data.records.map(r => r.id))

        const {records} = res.data
        return {records}
    }

    const deleteRecords = async (databaseId: string, ids: string[]) => {
        if (!socket || !isConnected) {
            onSocketError(defaultAPIMessage())
            return
        }

        const cb = async () => {
            setSelectedIds([])
            const res: SocketCallbackData<{}> = await socket.timeout(5000).emitWithAck(WorkspaceHandlerEvent.DeleteDatabaseRecords, {
                databaseId,
                ids,
                refPageId,
                refViewId
                // columnId: dbColumn.id
            })
            console.log("Delete database records res:", res)
        }

        confirm(`Are you sure you want to delete the selected records?`,
            `This cannot be undone.`,
            cb, undefined, undefined, true)
    }

    const directDeleteRecords = async (databaseId: string, ids: string[]) => {
        if (!socket || !isConnected) {
            onSocketError(defaultAPIMessage())
            return
        }

        setSelectedIds([])
        const res: SocketCallbackData<{}> = await socket.timeout(5000).emitWithAck(WorkspaceHandlerEvent.DeleteDatabaseRecords, {
            databaseId,
            ids,
            refPageId,
            refViewId
        })
        console.log("Direct delete database records res:", res)
        if (res.status !== 'ok') {
            const error = res.message || defaultAPIMessage()
            onSocketError(error)
            throw new Error(error)
        }
    }

    const requestedAdjacentDbRef = useRef([""])
    const requestFullAdjacentDatabase = async (databaseId: string, databaseRefId: string) => {
        if (requestedAdjacentDbRef.current.includes(databaseId)) return
        if (!socket || !isConnected) {
            onSocketError(defaultAPIMessage())
            return
        }
        requestedAdjacentDbRef.current.push(databaseId)
        // database, adjacentDatabases, records, permissions, page
        const d: SocketCallbackData<{
            records: Record[],
            adjacentDatabases: AdjacentDatabases
            database: Database,
            permissions: PagePermission[],
            accessLevel: AccessLevel,
            page: Page
        }> = await socket.timeout(SocketTimeoutMS).emitWithAck(WorkspaceHandlerEvent.RequestAdjacentDatabase, {
            databaseId,
            databaseRefId
        })
        if (!d || d.status === 'error') {
            const error = d?.message || defaultAPIMessage()
            updateDatabaseErrorState(databaseId, {error})
            toast.error(error)
            return
        }
        const {records, database, permissions, accessLevel, page, adjacentDatabases} = d.data

        const recordsIdMap: RecordsIdMap = {}
        for (const record of records) {
            recordsIdMap[record.id] = recordsIdMap[record.id] || {record}
            recordsIdMap[record.id].record = record
        }
        updateDatabaseErrorState(databaseId, {}, true)
        updateDatabasePageStore(databaseId, {
            database,
            permissions,
            accessLevel,
            page
        }, false)

        updateDatabaseStore(databaseId, database, recordsIdMap, true)
        addAdjacentDatabases(adjacentDatabases)

        requestedAdjacentDbRef.current = removeAllArrayItem(requestedAdjacentDbRef.current, databaseId)

    }

    const updateViewDefinition = async (viewId: string, pageId: string, update: Partial<ViewDefinition>) => {
        if (!socket || !isConnected) {
            toast.error(defaultAPIMessage())
            return
        }
        const res: SocketCallbackData<{}> = await socket.timeout(SocketTimeoutMS).emitWithAck(WorkspaceHandlerEvent.UpdatePageViewDefinition, {
            id: viewId,
            pageId,
            update
        })
        console.log("Update view definition res:", res)
        if (res.status !== 'ok') {
            toast.error(res.message || defaultAPIMessage())
            return
        }
    }

    const updateView = async (view: View, change: Partial<Pick<View, 'name' | 'isPublished' | 'description'>>) => {
        if (!isConnected || !socket) {
            toast.error(defaultAPIMessage())
            return
        }
        const {id, name, description, isPublished, pageId} = view

        const res: SocketCallbackData<{}> = await socket.timeout(SocketTimeoutMS).emitWithAck(WorkspaceHandlerEvent.UpdatePageView, {
            id,
            pageId,
            update: {
                name,
                isPublished,
                description,
                ...change
            }
        })
        console.log('UpdatePageView with response', res);
        if (res.status !== 'ok') {
            toast.error(res.message || defaultAPIMessage())
            return
        }
        return res
    }

    const deleteView = async (view: View) => {
        if (!isConnected || !socket) {
            toast.error(defaultAPIMessage())
            return
        }
        const {id, name, description, isPublished, pageId} = view

        return new Promise<void>((resolve, reject) => {
            const cb = async () => {

                const res: SocketCallbackData<{}> = await socket.timeout(SocketTimeoutMS).emitWithAck(WorkspaceHandlerEvent.DeletePageView, {
                    id,
                    pageId,
                })
                if (res.status !== 'ok') {
                    toast.error(res.message || defaultAPIMessage())
                }
                resolve();
            };

            confirm(
                `Are you sure you want to delete ${name}?`,
                'This cannot be undone',
                cb,
                () => {
                    // User canceled the confirmation
                    reject(new Error('User canceled the delete action'));
                }
            );
        });
    }

    const getActivities = async (databaseId: string, objectType: ActivityObjectType, objectId: string) => {
        if (!isConnected || !socket) {
            toast.error(defaultAPIMessage())
            return null
        }
        const res: SocketCallbackData<{
            activities: Activity[]
        }> = await socket.timeout(5000).emitWithAck(WorkspaceHandlerEvent.GetObjectActivities, {
            databaseId,
            objectType,
            objectId
        })
        if (res.status !== 'ok') {
            toast.error(res.message || defaultAPIMessage())
            return null
        }
        return res.data.activities
    }

    const generateAIField = async (databaseId: string, columnId: string, recordId: string) => {
        if (!isConnected || !socket) {
            toast.error(defaultAPIMessage())
            return
        }

        const colMetadata: AIColumnRecordMetadata = {
            inProgress: true,
            error: undefined,
            generatedAt: undefined,
            value: '',
            contentId: undefined
        }
        const meta: KeyValueStore = {}
        meta[RecordMetaKey('column', columnId)] = colMetadata

        await updateRecordValues(databaseId, [recordId], {}, meta)

        const res: SocketCallbackData<{}> = await socket.timeout(SocketTimeoutMS).emitWithAck(WorkspaceHandlerEvent.GenerateAICellValue, {
            databaseId,
            recordId,
            columnId,
            refPageId,
            refViewId
        })
        console.log("Generate AI Column Value cell values:", res)
        if (res.status !== 'ok') {
            const error = res.message || defaultAPIMessage()
            if (error.includes('credit')) {
                promptUpgrade(
                    'You have reached your limit for AI content generation. Please upgrade your plan or purchase credits to generate again.',
                    workspace.workspace.domain
                )
            }
            toast.error(error)
            return
        }
    }

    const deleteDatabaseColumn = async (databaseId: string, column: DatabaseColumn) => {
        if (!isConnected || !socket) {
            toast.error(defaultAPIMessage())
            return
        }
        const dbColumn = column
        confirm(`Are you sure you want to delete ${dbColumn.title}?`,
            `This cannot be undone, all tables and columns reading from ${dbColumn.title} will stop working`,
            async () => {
                const res: SocketCallbackData<{}> = await socket.timeout(SocketTimeoutMS).emitWithAck(WorkspaceHandlerEvent.DeleteDatabaseColumn, {
                    databaseId: databaseId,
                    columnId: dbColumn.id,
                    refPageId,
                    refViewId
                })
                console.log("Delete database column res:", res)
                if (res.status !== 'ok') {
                    toast.error(res.message || defaultAPIMessage())
                    return null
                }
            }, undefined, undefined, true)
    }

    const addDatabaseColumn = async (databaseId: string, column: DatabaseColumn): Promise<{ column: DatabaseColumn, database: Database } | undefined> => {
        if (!isConnected || !socket) {
            toast.error(defaultAPIMessage())
            return
        }

        const res: SocketCallbackData<{ database: Database }> = await socket.timeout(SocketTimeoutMS).emitWithAck(WorkspaceHandlerEvent.AddDatabaseColumn, {
            databaseId: databaseId,
            column,
            refViewId,
            refPageId
        })
        if (res.status !== 'ok') {
            toast.error(res.message || defaultAPIMessage())
            return
        }
        const database = res.data.database
        return {column, database}
    }

    const makeDatabaseColumnUnique = async (databaseId: string, columnId: string, unique: boolean): Promise<true | undefined> => {
        if (!isConnected || !socket) {
            toast.error(defaultAPIMessage())
            return
        }
        const res: SocketCallbackData<{}> = await socket.timeout(SocketTimeoutMS).emitWithAck(WorkspaceHandlerEvent.MakeDatabaseColumnUnique, {
            databaseId: databaseId,
            columnId,
            unique,
            refViewId,
            refPageId
        })
        if (res.status !== 'ok') {
            toast.error(res.message || defaultAPIMessage())
            return
        }
        return true
    }

    const pushDashboardTransactions = async (viewId: string, pageId: string, transactions: DashboardTransaction[]) => {
        if (!isConnected || !socket) {
            toast.error(defaultAPIMessage())
            return
        }

        const res: SocketCallbackData<{}> = await socket.timeout(SocketTimeoutMS).emitWithAck(WorkspaceHandlerEvent.PushDashboardTransaction, {
            viewId,
            pageId,
            transactions
        })
        if (res.status !== 'ok') {
            toast.error(res.message || defaultAPIMessage())
        }

    }

    const refreshDatabase = async (id: string, recordsAfter?: string) => {
        if (databaseErrorStore[id]?.loading) return;

        if (!token || !socket || !isConnected) {
            toast.error(defaultAPIMessage())
            return
        }
        updateDatabaseErrorState(id, {loading: true})
        const d: SocketCallbackData<{
            records: Record[],
            database: Database,
            permissions: PagePermission[],
            accessLevel: AccessLevel,
            page: Page,
            adjacentDatabases: AdjacentDatabases
        }> = await socket.timeout(SocketTimeoutMS).emitWithAck(WorkspaceHandlerEvent.RefreshDatabaseRecords, {
            id,
            recordsAfter,
            refViewId,
            refPageId
        })
        if (!d || d.status === 'error') {
            const error = d?.message || defaultAPIMessage()
            updateDatabaseErrorState(id, {error})
            toast.error(error)
            return
        }
        const {records, database, permissions, accessLevel, page, adjacentDatabases} = d.data

        const recordsIdMap: RecordsIdMap = {}
        for (const record of records) {
            recordsIdMap[record.id] = recordsIdMap[record.id] || {record}
            recordsIdMap[record.id].record = record
        }
        if (!databasePageStore[id]) {
            const item: PermissibleDatabaseWithPermissions = {
                database, page, permissions, views: []
            }
            addDatabase(item)
        }

        updateDatabaseErrorState(id, {}, true)
        updateDatabaseStore(id, database, recordsIdMap, true)
        updateDatabasePageStore(id, {
            database,
            permissions,
            accessLevel,
            page
        }, true)

        addAdjacentDatabases(adjacentDatabases)
    }

    // Record Tab View Functions
    const createRecordTabView = async (databaseId: string, viewDatabaseId: string, name: string, type: ViewType, definition: ViewDefinition) => {
        if (!socket || !isConnected) {
            toast.error(defaultAPIMessage())
            return
        }
        
        const res: SocketCallbackData<{ view: View }> = await socket.timeout(SocketTimeoutMS).emitWithAck(WorkspaceHandlerEvent.AddRecordView, {
            databaseId,
            viewDatabaseId,
            name,
            type,
            definition
        })
        
        if (res.status !== 'ok') {
            toast.error(res.message || defaultAPIMessage())
            return
        }
        
        return res.data.view
    }

    const updateRecordTabViewDefinition = async (databaseId: string, viewId: string, update: Partial<ViewDefinition>) => {
        if (!socket || !isConnected) {
            toast.error(defaultAPIMessage())
            return
        }

        const database = databaseStore[databaseId]?.database
        if (!database || !database.meta?.recordViewsMap?.[viewId]) {
            toast.error('View not found')
            return
        }

        const currentView = database.meta.recordViewsMap[viewId]
        const updatedView = {
            ...currentView,
            definition: {
                ...currentView.definition,
                ...update
            }
        }
        const updatedMeta = {
            ...database.meta,
            recordViewsMap: {
                ...database.meta.recordViewsMap,
                [viewId]: updatedView
            }
        }

        const res: SocketCallbackData<{}> = await socket.timeout(SocketTimeoutMS).emitWithAck(WorkspaceHandlerEvent.UpdateDatabaseMeta, {
            databaseId,
            meta: updatedMeta
        })

        if (res.status !== 'ok') {
            toast.error(res.message || defaultAPIMessage())
            return
        }
    }

    const updateRecordTabView = async (databaseId: string, viewId: string, change: Partial<Pick<View, 'name' | 'isPublished' | 'description'>>) => {
        if (!socket || !isConnected) {
            toast.error(defaultAPIMessage())
            return
        }

        const database = databaseStore[databaseId]?.database
        if (!database || !database.meta?.recordViewsMap?.[viewId]) {
            toast.error('View not found')
            return
        }

        const currentView = database.meta.recordViewsMap[viewId]
        const updatedView = {
            ...currentView,
            ...change
        }
        const updatedMeta = {
            ...database.meta,
            recordViewsMap: {
                ...database.meta.recordViewsMap,
                [viewId]: updatedView
            }
        }

        const res: SocketCallbackData<{}> = await socket.timeout(SocketTimeoutMS).emitWithAck(WorkspaceHandlerEvent.UpdateDatabaseMeta, {
            databaseId,
            meta: updatedMeta
        })

        if (res.status !== 'ok') {
            toast.error(res.message || defaultAPIMessage())
            return
        }
        
        return res
    }

    const deleteRecordTabView = async (databaseId: string, viewId: string, viewName: string) => {
        if (!socket || !isConnected) {
            toast.error(defaultAPIMessage())
            return
        }

        const database = databaseStore[databaseId]?.database
        if (!database || !database.meta?.recordViewsMap?.[viewId]) {
            toast.error('View not found')
            return
        }

        const newRecordViewsMap = { ...database.meta.recordViewsMap }
        delete newRecordViewsMap[viewId]

        const newRecordViewsOrder = (database.meta.recordViewsOrder || []).filter(id => id !== viewId)

        const updatedMeta = {
            ...database.meta,
            recordViewsMap: newRecordViewsMap,
            recordViewsOrder: newRecordViewsOrder
        }

        const res: SocketCallbackData<{}> = await socket.timeout(SocketTimeoutMS).emitWithAck(WorkspaceHandlerEvent.UpdateDatabaseMeta, {
            databaseId,
            meta: updatedMeta
        })

        if (res.status !== 'ok') {
            toast.error(res.message || defaultAPIMessage())
            return
        }
    }

    const reorderRecordTabViews = async (databaseId: string, tabIds: string[]) => {
        if (!socket || !isConnected) {
            toast.error(defaultAPIMessage())
            return
        }

        const database = databaseStore[databaseId]?.database
        if (!database) {
            toast.error('Database not found')
            return
        }

        const updatedMeta = {
            ...database.meta,
            recordViewsOrder: tabIds
        }

        const res: SocketCallbackData<{}> = await socket.timeout(SocketTimeoutMS).emitWithAck(WorkspaceHandlerEvent.UpdateDatabaseMeta, {
            databaseId,
            meta: updatedMeta
        })

        if (res.status !== 'ok') {
            toast.error(res.message || defaultAPIMessage())
            return
        }
    }

    const smartUpdateViewDefinition = async (viewId: string, pageId: string, update: Partial<ViewDefinition>, context?: { databaseId?: string, isRecordTab?: boolean }) => {
        if (context?.isRecordTab && context?.databaseId) {
            return updateRecordTabViewDefinition(context.databaseId, viewId, update)
        }
        return updateViewDefinition(viewId, pageId, update)
    }

    return <ViewsContext.Provider
        value={{
            ...props,
            selectedIds,
            setSelectedIds,
            sorts,
            filter,
            setFilter,
            setSorts,
            search,
            setSearch,
            pushDashboardTransactions,
            refreshDatabase,
            uploadWorkspaceFile,
            updateRecordValues,
            updateDatabaseColumn,
            updateDatabaseTitleColumn,
            uploadQueue: uploadQueueRef.current,
            makeDatabaseColumnUnique,
            updateView,
            uploadFileToColumn,
            previewFiles,
            createRecords,
            requestFullAdjacentDatabase,
            updateViewDefinition,
            deleteRecords,
            directDeleteRecords,
            cache,
            generateAIField,
            getActivities,
            deleteDatabaseColumn,
            addDatabaseColumn,
            deleteView,
            peekRecordId,
            setPeekRecordId,
            peekDatabaseId,
            setPeekRecord,
            createRecordTabView,
            updateRecordTabViewDefinition,
            updateRecordTabView,
            deleteRecordTabView,
            reorderRecordTabViews,
            smartUpdateViewDefinition
        }}>
        {props.children}
    </ViewsContext.Provider>
}

export interface ViewsContextProps extends ViewsProviderProps {
    filter: DbRecordFilter
    sorts: DbRecordSort[]
    setFilter: (filter: DbRecordFilter) => void
    setSorts: (sorts: DbRecordSort[]) => void
    search: string
    setSearch: (search: string) => void
    peekRecordId?: string
    setPeekRecordId: (id: string) => void
    peekDatabaseId?: string
    setPeekRecord: (databaseId: string, recordId: string) => void
    selectedIds: string[]
    setSelectedIds: (ids: string[]) => void
    uploadQueue: FileUploadQueue
    refreshDatabase: (id: string, recordsAfter?: string) => Promise<void>
    pushDashboardTransactions: (viewId: string, pageId: string, transactions: DashboardTransaction[]) => Promise<void>
    previewFiles: (files: FileItem[], index: number) => void
    uploadWorkspaceFile: (namespace: string, seed: string, uploadKey: string, file: File, onComplete: (res: BackendAPIResponse<{
        upload: WorkspaceUpload
    }>) => void) => void
    updateRecordValues: (databaseId: string, recordIds: string[], values: RecordValues, meta?: KeyValueStore) => Promise<true | void>
    updateDatabaseTitleColumn: (databaseId: string, columnId: string) => Promise<void>
    updateDatabaseColumn: (databaseId: string, columnId: string, type: DatabaseFieldDataType, column: Partial<DatabaseColumn>) => Promise<true | void>
    deleteDatabaseColumn: (databaseId: string, column: DatabaseColumn) => void
    makeDatabaseColumnUnique: (databaseId: string, columnId: string, unique: boolean) => Promise<true | undefined>
    addDatabaseColumn: (databaseId: string, column: DatabaseColumn) => Promise<{ column: DatabaseColumn, database: Database } | undefined>
    uploadFileToColumn: (databaseId: string, recordId: string, columnId: string, file: File) => void
    generateAIField: (databaseId: string, columnId: string, recordId: string) => void
    createRecords: (databaseId: string, values: RecordValues[], meta?: KeyValueStore, onDuplicate?: OnDuplicateAction) => Promise<{
        records: Record[]
    } | undefined | void>
    updateViewDefinition: (viewId: string, pageId: string, update: Partial<ViewDefinition>) => Promise<void>
    updateView: (view: View, change: Partial<Pick<View, 'name' | 'isPublished' | 'description'>>) => Promise<SocketCallbackData<any> | undefined>
    deleteView: (view: View) => Promise<void>
    requestFullAdjacentDatabase: (databaseId: string, databaseRefId: string) => void
    getActivities: (databaseId: string, objectType: ActivityObjectType, objectId: string) => Promise<Activity[] | null>
    deleteRecords: (databaseId: string, ids: string[]) => Promise<void>
    directDeleteRecords: (databaseId: string, ids: string[]) => Promise<void>
    cache: CacheMethods<any>
    createRecordTabView: (databaseId: string, viewDatabaseId: string, name: string, type: ViewType, definition: ViewDefinition) => Promise<View | undefined>
    updateRecordTabViewDefinition: (databaseId: string, viewId: string, update: Partial<ViewDefinition>) => Promise<void>
    updateRecordTabView: (databaseId: string, viewId: string, change: Partial<Pick<View, 'name' | 'isPublished' | 'description'>>) => Promise<SocketCallbackData<any> | undefined>
    deleteRecordTabView: (databaseId: string, viewId: string, viewName: string) => Promise<void>
    reorderRecordTabViews: (databaseId: string, tabIds: string[]) => Promise<void>
    smartUpdateViewDefinition: (viewId: string, pageId: string, update: Partial<ViewDefinition>, context?: { databaseId?: string, isRecordTab?: boolean }) => Promise<void>
}

export const ViewsContext = createContext<ViewsContextProps | undefined>(
    undefined
);

export const useViews = () => {
    const context = useContext(ViewsContext)
    if (!context) throw new Error('useViews must be used within a ViewsProvider');
    return context as ViewsContextProps;
}

// Hook that provides only filtering state in a context-aware way
export const useViewFiltering = () => {
    const mainViews = useViews()
    const recordTabContext = useContext(RecordTabViewsContext)
    
    if (recordTabContext) {
        return {
            filter: recordTabContext.filter,
            sorts: recordTabContext.sorts,
            search: recordTabContext.search,
            setFilter: recordTabContext.setFilter,
            setSorts: recordTabContext.setSorts,
            setSearch: recordTabContext.setSearch
        }
    }
    
    return {
        filter: mainViews.filter,
        sorts: mainViews.sorts,
        search: mainViews.search,
        setFilter: mainViews.setFilter,
        setSorts: mainViews.setSorts,
        setSearch: mainViews.setSearch
    };
}
