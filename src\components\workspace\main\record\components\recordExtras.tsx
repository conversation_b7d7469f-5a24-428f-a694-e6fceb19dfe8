"use client";

import {<PERSON><PERSON>} from "@/components/ui/button";
import {ViewIcon} from "@/components/workspace/main/views/viewIcon";
import {subLinks} from "@/utils/demo/links";
import React, {useEffect, useState} from "react";
import {DropdownMenu, DropdownMenuContent, DropdownMenuTrigger, DropdownMenuItem, DropdownMenuGroup, DropdownMenuSeparator} from "@/components/ui/dropdown-menu";
import {ArrowPathIcon, CogIcon, EllipsisHorizontalIcon, GlobeAltIcon, PlusCircleIcon, TrashIcon} from "@heroicons/react/24/outline";
import Link from "next/link";
import {DNDSortable, SortItem} from "@/components/custom-ui/dndSortable";
import {SubLink} from "@/components/workspace/main/common/navLinks";
import {RecordActivities} from "@/components/workspace/main/record/components/extra/recordActivities";
import {Tab, TabView} from "@/components/custom-ui/tabView";
import {WorkspaceReminders} from "@/components/workspace/main/common/workspaceReminders";
import {BoltIcon, BookIcon, ChartPieIcon, ClockThreeIcon, MessageDotsIcon, NoteIcon} from "@/components/icons/FontAwesomeRegular";
import {ViewCreator} from "@/components/workspace/main/views/common/viewCreator";
import {useScreenSize} from "@/providers/screenSize";
import {RecordSummary} from "@/components/workspace/main/record/components/extra/recordSummary";
import dynamic from "next/dynamic";
import {WorkspaceNotes} from "@/components/workspace/main/common/workspaceNotes";
import {useRecord} from "@/providers/record";
import {getDatabaseTitleCol, getRecordTitle} from "@/components/workspace/main/views/form/components/element/linked";
import {WorkspaceMemberSettingsKey} from "@/typings/workspace";
import {isLocal} from "@/utils/environment";
import {useWorkspace} from "@/providers/workspace";
import {View} from "@/typings/page";
import {ViewRender} from "@/components/workspace/main/views/viewRender";
import {ViewsRootLayout} from "@/components/workspace/main/views/ViewsRootLayout";
import {ViewType} from "opendb-app-db-utils/lib/typings/view";
import {DatabaseRefreshWrap} from "@/components/workspace/main/database/databaseRootLayout";
import {useViews} from "@/providers/views";
import {InputWithEnter} from "@/components/custom-ui/inputWithEnter";
import {useAlert} from "@/providers/alert";
import {RecordTabViewsProvider} from "@/providers/recordTabViews";
import {useSearchParams, useRouter, usePathname} from "next/navigation";

const RecordOverview = dynamic(
    () => import('@/components/workspace/main/record/components/recordOverview').then((module) => module.RecordOverview),
    {ssr: false}
    // { loading: () => <p>Loading...</p> }
);

export const RecordExtras = ({showOverview, activeTab}: {showOverview?: boolean, activeTab?: string | null}) => {
    const {recordInfo, database} = useRecord()
    const {id, databaseId} = recordInfo.record
    const {isMobile} = useScreenSize()
    const {databasePageStore} = useWorkspace()
    const searchParams = useSearchParams()
    const router = useRouter()
    const pathname = usePathname()

    console.log('🎭 RecordExtras render:', {
        recordId: id,
        databaseId,
        showOverview,
        activeTab,
        isMobile,
        timestamp: new Date().toISOString()
    });

    const [activeTabId, setActiveTabId] = useState<string | null>(null);

    const {defaultTitle, isContacts, titleColId} = getDatabaseTitleCol(database)
    const title = getRecordTitle(recordInfo.record, titleColId, defaultTitle, isContacts, database)

    const recordTabsOrder = database.meta?.recordViewsOrder || []
    const recordTabsMap = database.meta?.recordViewsMap || {}

    // Find and render views that are specifically for record tabs
    const viewTabs: Tab[] = Object.values(recordTabsMap).map(v => {
        const viewDatabaseId = (v.definition as any)?.databaseId;
        return {
            id: v.id,
            title: <>
                <ViewIcon type={v.type} className="size-3"/>
                <div className="overflow-hidden truncate flex-1 font-semibold">
                    {v.name}
                </div>
            </>,
            content: <div className="h-full w-full overflow-hidden">
                {viewDatabaseId ? (
                    <DatabaseRefreshWrap id={viewDatabaseId} refreshOnInitialRender>
                        <ViewsRootLayout context="record_tab" parentId={databaseId} viewId={v.id}>
                            <ViewRender id={v.id} view={v}/>
                        </ViewsRootLayout>
                    </DatabaseRefreshWrap>
                ) : (
                    <ViewsRootLayout context="record_tab" parentId={databaseId} viewId={v.id}>
                        <ViewRender id={v.id} view={v}/>
                    </ViewsRootLayout>
                )}
            </div>
        };
    });

    // Define the default tabs
    const defaultTabs: Tab[] = [{
        id: "summary",
        title: <>
            <BookIcon className="size-3"/>
            <div className="overflow-hidden truncate flex-1 font-semibold">
                Summary
            </div>
        </>,
        content: <RecordSummary/>
    },
        {
            id: "activity",
            title: <>
                <BoltIcon className="size-3"/>
                <div className="overflow-hidden truncate flex-1 font-semibold">
                    Activities
                </div>
            </>,
            content: <RecordActivities/>
        },
        {
            id: "notes",
            title: <>
                <NoteIcon className="size-3"/>
                <div className="overflow-hidden truncate flex-1 font-semibold">
                    Notes
                </div>
            </>,
            content: <><WorkspaceNotes recordId={id} databaseId={databaseId}/></>
        },
        {
            id: "reminders",
            title: <>
                <ClockThreeIcon className="size-3"/>
                <div className="overflow-hidden truncate flex-1 font-semibold">
                    Reminders
                </div>
            </>,
            content: <><WorkspaceReminders
                newReminderTitle={`Remind me about ${title}`}
                recordId={id} databaseId={databaseId}/></>
        }]

    const tabs: Tab[] = [...defaultTabs, ...viewTabs]

    if (isMobile || showOverview) {
        tabs.unshift({
            id: "overview",
            title: <>
                <ChartPieIcon className="size-3"/>
                <div className="overflow-hidden truncate flex-1 font-semibold">
                    Overview
                </div>
            </>,
            content: <RecordOverview isTabbed/>
        },)
    }

    console.log('📋 RecordExtras tabs created:', {
        totalTabs: tabs.length,
        tabIds: tabs.map(tab => tab.id),
        viewTabsCount: viewTabs.length,
        viewTabIds: viewTabs.map(tab => tab.id),
        hasOverview: tabs.some(tab => tab.id === 'overview'),
        showOverview,
        isMobile
    });

    if (isLocal()) {
        tabs.push({
            id: "custom-1",
            title: <>
                <MessageDotsIcon className="size-3"/>
                <div className="overflow-hidden truncate flex-1 font-semibold">
                    Stories
                </div>
            </>,
            content: <>Stories</>
        })
    }

    if (recordTabsOrder.length > 0) {
        const reorderableTabs = tabs.filter(tab => tab.id !== "overview")
        const overviewTab = tabs.find(tab => tab.id === "overview")

        const tabMap: {[key: string]: Tab} = {}
        reorderableTabs.forEach(tab => {
            tabMap[tab.id] = tab
        })

        const orderedTabs: Tab[] = []

        if (overviewTab) {
            orderedTabs.push(overviewTab)
        }

        recordTabsOrder.forEach(id => {
            if (tabMap[id]) {
                orderedTabs.push(tabMap[id])
                delete tabMap[id]
            }
        })

        Object.values(tabMap).forEach(tab => {
            orderedTabs.push(tab)
        })

        if (orderedTabs.length > 0) {
            tabs.length = 0
            tabs.push(...orderedTabs)
        }
    }

    useEffect(() => {
        const tabFromUrl = searchParams.get('tab')
        const targetTab = activeTab || tabFromUrl

        console.log('🎯 RecordExtras tab selection logic:', {
            tabFromUrl,
            activeTab,
            targetTab,
            currentActiveTabId: activeTabId,
            availableTabIds: tabs.map(tab => tab.id),
            tabsLength: tabs.length
        });

        if (targetTab && tabs.some(tab => tab.id === targetTab)) {
            console.log('✅ Setting target tab:', targetTab);
            setActiveTabId(targetTab)
        } else if (!activeTabId && tabs.length > 0) {
            console.log('📌 Setting default tab:', tabs[0].id);
            setActiveTabId(tabs[0].id)
        }
    }, [tabs, activeTabId, activeTab, searchParams])

    const handleTabChange = (tabId: string | null) => {
        setActiveTabId(tabId)
        
        if (tabId) {
            const params = new URLSearchParams(searchParams.toString())
            params.set('tab', tabId)
            
            params.delete('noteId')
            params.delete('reminderId')
            
            const newQuery = params.toString()
            const newPath = pathname + (newQuery ? `?${newQuery}` : '')
            
            router.replace(newPath, { scroll: false })
        }
    }

    const handleNewView = (view: View) => {
        handleTabChange(view.id)
    }

    return <>
    
        <TabView tabs={tabs}
                 activeTab={activeTabId}
                 onTabChange={handleTabChange}
                 key={isMobile ? 1 : 2}
                 tabSwitcherClassName="px-3"
                 className="extra rEx"
                 tabTitleExtra={<RecordTabOptions tabs={tabs} onNewView={handleNewView} setActiveTabId={handleTabChange}/>}/>
    
    </>
}

interface RecordTabOptionsProps {
    tabs: Tab[];
    onNewView: (view: View) => void;
    setActiveTabId: (id: string | null) => void;
}

const RecordTabOptions = ({ tabs, onNewView, setActiveTabId }: RecordTabOptionsProps) => {
    const [open, setOpen] = useState(false)
    const [newView, setNewView] = useState(false)
    const {deleteView, updateView, updateRecordTabView, deleteRecordTabView, reorderRecordTabViews} = useViews()
    const {databasePageStore, databaseStore} = useWorkspace()
    const {recordInfo, database} = useRecord()
    const {confirm} = useAlert()

    const handleNewViewCreated = (view: View) => {
        const currentTabIds = tabs
            .filter(tab => tab.id !== "overview")
            .map(tab => tab.id)
        
        const newOrder = [...currentTabIds, view.id]
        reorderRecordTabViews(database.id, newOrder)
        onNewView(view)
    }

    const reorderableTabs = tabs.filter(tab => tab.id !== "overview")

    // Get view object for tab (only custom views have view objects)
    const getViewForTab = (tabId: string): View | undefined => {
        return database.meta?.recordViewsMap?.[tabId]
    }

    const saveTabOrder = (tabIds: string[]) => {
        reorderRecordTabViews(database.id, tabIds)
    }

    const doDelete = (view: View) => {
        const currentIndex = tabs.findIndex(tab => tab.id === view.id); 
        
        deleteRecordTabView(database.id, view.id, view.name);

        if (tabs.length > 1) {
            const nextTabIndex = currentIndex === tabs.length - 1 
                ? currentIndex - 1 
                : currentIndex + 1;
        
            const nextTab = tabs[nextTabIndex];
            if (nextTab && nextTab.id !== 'overview') {
                setActiveTabId(nextTab.id);
            } else if (tabs.length > 1) {
                const fallbackTab = tabs.find(tab => tab.id !== 'overview');
                if (fallbackTab) {
                    setActiveTabId(fallbackTab.id);
                }
            }
        } else {
            setActiveTabId(null);
        }
    }

    const resetTabOrder = () => {
        reorderRecordTabViews(database.id, [])
    }

    const saveChange = (view: View, change: Partial<View>) => {
        setOpen(false);
        
        if (database.meta?.recordViewsMap?.[view.id]) {
            updateRecordTabView(database.id, view.id, change)
        } else {
            updateView(view, {
                name: view.name,
                isPublished: view.isPublished,
                description: view.description,
                ...change
            }).finally(() => {
            });
        }
    }

    return <>
        <ViewCreator context='record_tab' setOpen={setNewView} open={newView} onViewCreated={handleNewViewCreated}/>
        <div className="p-2">
            <DropdownMenu open={open} onOpenChange={setOpen}>
                <DropdownMenuTrigger asChild>
                    <Button variant="ghost"
                            className="text-xs rounded-full p-1.5 h-auto gap-2 overflow-hidden">
                        <CogIcon className="size-4"/>
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-96 p-0 rounded-none" align="end">
                    <div className="flex flex-col h-auto max-h-96">
                        <div className="p-2 border-b flex-1 overflow-y-auto max-h-64">
                            <DNDSortable
                                items={reorderableTabs.map(tab => ({id: tab.id, data: tab}))}
                                itemRenderer={function (index: number, item: SortItem<Tab>): React.ReactNode {
                                    const tab = item.data
                                    const view = getViewForTab(tab.id);
                                    const isCustomView = !!view; // Only custom tab views can be deleted
                                    
                                    return <Button variant="ghost"
                                                   className="text-xs rounded-none p-1.5 px-2 h-auto gap-2 w-full justify-start overflow-hidden">
                                        <div className="flex items-center flex-1 gap-2 text-left justify-start">
                                            {tab.title}
                                        </div>
                                        <div className="flex items-center">
                                            <DropdownMenu>
                                                <DropdownMenuTrigger asChild>
                                            <Button variant="ghost"
                                                    onClick={(e) => {
                                                        e.stopPropagation()
                                                        e.preventDefault()
                                                    }}
                                                    className="size-6 p-1 rounded-full items-center hover:bg-neutral-300">
                                                <EllipsisHorizontalIcon className="h-3 w-3"/>
                                            </Button>
                                                </DropdownMenuTrigger>
                                                <DropdownMenuContent className="w-56" align="end">
                                                    {isCustomView && (
                                                        <>
                                                            <DropdownMenuGroup className="p-1 pb-0">
                                                                <InputWithEnter
                                                                    placeHolder="Name"
                                                                    value={view?.name || ""}
                                                                    onChange={(v) => {
                                                                        if (v && view) saveChange(view, {name: v});
                                                                    }}
                                                                    shortEnter
                                                                    disabled={false}
                                                                    wrapperClassname="h-8 p-1"
                                                                />
                                                            </DropdownMenuGroup>
                                                            <DropdownMenuSeparator />
                                                            <DropdownMenuGroup className="p-1">
                                                                <DropdownMenuItem 
                                                                    className="text-xs rounded-none p-1.5 h-7"
                                                                    onClick={() => {
                                                                        if (view) {
                                                                            confirm(
                                                                                `Are you sure you want to delete ${view.name}?`,
                                                                                'This action cannot be undone.',
                                                                                () => doDelete(view)
                                                                            )
                                                                        }
                                                                    }}>
                                                                    Delete
                                                                </DropdownMenuItem>
                                                            </DropdownMenuGroup>
                                                        </>
                                                    )}
                                                    {!isCustomView && (
                                                        <DropdownMenuItem disabled className="text-xs text-gray-400">
                                                            No actions available
                                                        </DropdownMenuItem>
                                                    )}
                                                </DropdownMenuContent>
                                            </DropdownMenu>
                                        </div>
                                    </Button>
                                }}
                                onChange={function (items: SortItem<Tab>[]): void {
                                    const tabIds = items.map(item => item.id)
                                    saveTabOrder(tabIds)
                                }}
                                useDragHandle
                                handlePosition={"center"}
                                wrapperClassName={(index, item) => {
                                    return `hover:bg-neutral-100 gap-0.5 pl-2`
                                }}
                            />
                        </div>
                        <div className="p-2">
                            <Button variant="ghost"
                                    className="text-xs rounded-none p-1.5 h-auto gap-2 w-full justify-start"
                                    onClick={resetTabOrder}>
                                <ArrowPathIcon className="size-4"/>
                                Reset to Default Order
                            </Button>
                        </div>
                        <div className="p-2 border-t">
                            <Button variant="ghost"
                                    className="text-xs rounded-none p-1.5 h-auto gap-2 w-full justify-start"
                                    onClick={() => {
                                        setOpen(false)
                                        setNewView(true)
                                    }}>
                                <PlusCircleIcon className="size-4"/>
                                New View
                            </Button>
                        </div>
                    </div>
                </DropdownMenuContent>
            </DropdownMenu>
        </div>
    </>
}