"use client"

import {RenderCellProps, RenderEditCellProps} from "react-data-grid";
import React, {useState} from "react";
import {DropdownMenu, DropdownMenuContent, DropdownMenuGroup, DropdownMenuItem, DropdownMenuPortal, DropdownMenuSeparator, DropdownMenuSub, DropdownMenuSubContent, DropdownMenuSubTrigger, DropdownMenuTrigger} from "@/components/ui/dropdown-menu";
import {cn} from "@/lib/utils";
import {ArrowDownCircleIcon, ChevronUpDownIcon} from "@heroicons/react/24/outline";
import {Button} from "@/components/ui/button";
import {Label} from "@/components/ui/label";
import {HeaderEditDropDownOptionsProps} from "@/components/workspace/main/views/table/renderer/fields/text";
import {FileItem, FilesColumn, FilesColumnDbValue, RecordValues} from "opendb-app-db-utils/lib/typings/db";
import {CheckIcon as FACheckIcon, DownloadIcon, TrashIcon, UpRightAndDownLeftFromCenterIcon} from "@/components/icons/FontAwesomeRegular";
import {Loader} from "@/components/custom-ui/loader";
import {useViews} from "@/providers/views";
import {Record} from "@/typings/database";
import {DataViewRow, RGDMeta} from "@/components/workspace/main/views/table";
import {Accept, useDropzone} from "react-dropzone";
import {useAlert} from "@/providers/alert";
import {GridRender} from "@/components/workspace/main/views/table/renderer/common/gridRender";
import {formatByteSize} from "opendb-app-db-utils/lib";

const mimeExt = require('mime-ext');

export interface UploadItem {
    id: string
    progress: number
}

export const FileRenderer = <R, SR>(props: RenderCellProps<R, SR>): React.ReactNode => {

    const {column,} = props

    const rowData = props.row as DataViewRow
    const row = rowData.record
    // @ts-ignore
    const meta: RGDMeta = column['__meta__']
    return <>
        <GridRender rowId={rowData.id}>
            <div className="r-files h-full w-full">
                <FileHandler
                    databaseId={meta.databaseId}
                    columnId={meta.column.id}
                    row={row}/>
            </div>
        </GridRender>
    </>
};

export const FileEditor = <TRow, TSummaryRow>(props: RenderEditCellProps<TRow, TSummaryRow>): React.ReactNode => {
    const {column,} = props

    const rowData = props.row as DataViewRow
    const row = rowData.record
    // @ts-ignore
    const meta: RGDMeta = column['__meta__']

    return <>
        <GridRender rowId={rowData.id}>
            <div className="r-files h-full w-full">
                <FileHandler
                    databaseId={meta.databaseId}
                    columnId={meta.column.id}
                    row={row}
                    canDelete
                    showDropLabel
                    canAddFiles
                    canExpand
                    defaultExpanded/>
            </div>
        </GridRender>
    </>
};

interface FileHandlerProps {
    defaultExpanded?: boolean
    showDropLabel?: boolean
    canAddFiles?: boolean

    canExpand?: boolean
    canDelete?: boolean
    onDelete?: () => void
    databaseId: string
    columnId: string
    row: Record
}

export const FileHandler = (props: FileHandlerProps) => {
    const {uploadQueue} = useViews()
    const {row, columnId, databaseId} = props
    const {uploadFileToColumn, previewFiles, updateRecordValues} = useViews()
    const {confirm} = useAlert()

    let rowVal = row.recordValues[columnId] as (FilesColumnDbValue | undefined)
    let files = rowVal && Array.isArray(rowVal) ? rowVal : []

    const uploads = uploadQueue[databaseId] &&
                    uploadQueue[databaseId][row.id] &&
                    uploadQueue[databaseId][row.id][columnId] ?
                    Object.values(uploadQueue[databaseId][row.id][columnId]) : []

    const doDelete = (fileItem: FileItem) => {

        confirm(`Delete ${fileItem.name}?`, `This will delete ${fileItem.name} and cannot be undone`, () => {
            const items = files.filter(f => f.id !== fileItem.id)
            const values: RecordValues = {}
            values[columnId] = items
            updateRecordValues(databaseId, [row.id], values)
        })
    }

    return <>
        <FileHandlerUI
            defaultExpanded={props.defaultExpanded}
            wrapperClassName='h-full'
            files={files}
            uploads={uploads}
            onDelete={doDelete}
            showDropLabel={props.showDropLabel}
            onPreview={previewFiles}
            canExpand={props.canExpand}
            uploadDisabled={!props.canAddFiles}
            canDelete={props.canDelete}
            onNewUpload={file => uploadFileToColumn(databaseId, row.id, columnId, file)}
        />
    </>
}

export interface FileHandlerUIProps {
    files: FileItem[]
    uploads?: UploadItem[]
    onDelete: (item: FileItem) => void
    onPreview: (item: FileItem) => void
    onNewUpload: (file: File) => void
    uploadDisabled?: boolean
    canDelete?: boolean
    canExpand?: boolean
    defaultExpanded?: boolean
    showDropLabel?: boolean
    wrapperClassName?: string
}

export const FileHandlerUI = (props: FileHandlerUIProps) => {
    const {
        files,
        uploads,
        onDelete,
        onNewUpload,
        onPreview,
        canDelete,
        canExpand,
        uploadDisabled,
        defaultExpanded,
        showDropLabel,
        wrapperClassName
    } = props

    const [open, setOpen] = useState(defaultExpanded)

    const onDrop = (acceptedFiles: File[]) => {
        for (const file of acceptedFiles) {
            onNewUpload(file)
        }
    }

    const previewFiles = (files: FileItem[], index: number) => {
        onPreview(files[index])
    }

    const doDelete = (fileItem: FileItem) => {
        onDelete(fileItem)
    }

    // @ts-ignore
    const isSafari = window['safari'] !== undefined;

    const {getRootProps, getInputProps, isDragActive, inputRef} = useDropzone({
        onDrop,
        maxFiles: 10,
        disabled: uploadDisabled,
        noClick: isSafari,
        noKeyboard: isSafari
    })
    const openDz = () => {
        inputRef.current?.click()
    }
    return <>
        <div
            className={cn("text-xs h-9 flex items-center gap-1 w-full select-none overflow-hidden", wrapperClassName)}
        >
            <div className='flex-1 overflow-hidden'>
                <div {...getRootProps({className: 'flex gap-1 flex-nowrap', onClick: undefined})}>

                    {(showDropLabel || isDragActive) && <>
                        {!uploadDisabled && <div className='hidden'><input {...getInputProps()} type="file" className="hidden"/></div>}
                        <div className={`${uploads?.length === 0 && files.length === 0 && 'w-full'} px-1`}>
                            <Button variant="ghost"
                                    disabled={uploadDisabled}
                                    onClick={openDz}
                                    className={`flex-none text-xs text-neutral-500 hover:bg-neutral-300 h-6 block overflow-hidden p-1 items-center rounded-none
                                        ${isDragActive && 'ring-1 ring-black'} w-full
                                    `}>
                                <div className="flex gap-1.5">
                                    <ArrowDownCircleIcon className="size-4"/>
                                    {!isDragActive && <span>Drop files</span>}
                                    {isDragActive && <span>Drop files to upload</span>}
                                </div>
                            </Button>
                        </div>

                    </>}
                    {uploads?.map((o, i) => {
                        return <div key={o.id}
                                    className="flex-none bg-neutral-300 size-6 block overflow-hidden relative">
                            <div className="size-full flex items-center justify-center rounded-md">
                                <Loader className="size-3 m-0" theme='dark'/>
                            </div>
                        </div>
                    })}
                    {files.map((o, i) => {
                        const isImage = o.type.includes("image");

                        const ext = mimeExt(o.type)[0] || '-'
                        return <div key={i}
                                    onClick={(e) => {
                                        e.stopPropagation()
                                        e.preventDefault()
                                        previewFiles(files, i)
                                    }}
                                    className="flex-none bg-neutral-300 size-6 block overflow-hidden">
                            {isImage ? (
                                <div className="bg-contain bg-center h-full w-full" style={{backgroundImage: `url(${o.link})`}}/>
                            ) : (
                                 <div className="text-xs !text-[9px] flex items-center justify-center w-full h-full uppercase truncate font-semibold ">{ext}</div>
                             )}
                        </div>
                    })}
                </div>
            </div>
            {canExpand && <DropdownMenu open={open} onOpenChange={setOpen}>
                <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="p-0.5 rounded-full size-5 hover:bg-neutral-300 mr-1"
                            disabled={uploadDisabled}>
                        <ChevronUpDownIcon className="size-4"/>
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-80 p-0 rounded-none" align="end">
                    <DropdownMenuGroup className="p-2">
                        <div {...getRootProps({className: 'block'})}>
                            <div className="hidden">
                                <input type='file' className="hidden" {...getInputProps()}/>
                            </div>
                            <Button variant="outline"
                                    onClick={openDz}
                                    className={`flex-none text-xs text-neutral-500 hover:bg-neutral-300 block overflow-hidden p-1 items-center rounded-none w-full
                                     ${isDragActive && 'ring-1 ring-black'}
                                    `}>
                                <div className="flex gap-1.5">
                                    <ArrowDownCircleIcon className="size-4"/>
                                    {!isDragActive && <span>Add/drop files</span>}
                                    {isDragActive && <span>Drop files to upload</span>}
                                </div>
                            </Button>
                        </div>
                    </DropdownMenuGroup>
                    <div className="overflow-hidden overflow-y-auto h-auto min-h-0 max-h-80">
                        <div className="flex flex-col">
                            <DropdownMenuSeparator className="my-0"/>
                            {uploads?.length > 0 && <>
                                <DropdownMenuGroup className="p-2">
                                    <div className="text-xs font-semibold mb-2">Uploading</div>
                                    <div className="flex flex-col gap-1.5">
                                        {uploads.map((o, i) => {
                                            return <div key={o.id} className="flex gap-1 w-full items-center">
                                                <div className="bg-neutral-300 size-11 overflow-hidden relative block">
                                                    <div
                                                        className="size-full flex items-center justify-center rounded-md">
                                                        <Loader className="size-4 m-0" theme='dark'/>
                                                    </div>
                                                </div>
                                                <div className="text-xs text-[10px] font-medium text-muted-foreground">
                                                    Uploading...({o.progress}%)
                                                </div>
                                            </div>
                                        })}
                                    </div>
                                </DropdownMenuGroup>
                                <DropdownMenuSeparator className="my-0"/>
                            </>}
                            {files.length > 0 && <>
                                <DropdownMenuGroup className="p-2">
                                    <div className="text-xs font-semibold mb-2">Uploads</div>
                                    <div className="flex flex-col gap-1.5">
                                        {files.map((o, i) => {
                                            const isImage = o.type.includes("image");
                                            const ext = mimeExt(o.type)[0] || '-'

                                            return <div key={o.id} className="flex gap-1.5 w-full">
                                                <div className="bg-neutral-300 size-11 overflow-hidden relative block"
                                                     onClick={(e) => {
                                                         e.preventDefault()
                                                         e.stopPropagation()
                                                         previewFiles(files, i)
                                                     }}>
                                                    {isImage ? (
                                                        <div className="bg-contain bg-center h-full w-full"
                                                             style={{backgroundImage: `url(${o.link})`}}/>
                                                    ) : (
                                                         <div className="text-xs text-[10px] flex items-center justify-center w-full h-full font-bold uppercase truncate">{ext}</div>
                                                     )}
                                                </div>
                                                <div className="flex-1 overflow-hidden">
                                                    <div className="flex flex-col gap-0.5">
                                                        <Label
                                                            className="w-full truncate overflow-hidden font-semibold text-xs">
                                                            {o.name}
                                                        </Label>
                                                        <div className="flex gap-1 items-center">
                                                            {o.size && <>
                                                                <span className='text-xs font-medium text-muted-foreground'>{formatByteSize(o.size)}</span>
                                                            </>}
                                                            <Button variant="ghost" size="sm"
                                                                    onClick={(e) => {
                                                                        e.preventDefault()
                                                                        e.stopPropagation()
                                                                        previewFiles(files, i)
                                                                    }}
                                                                    className="p-1 rounded-full h-6 text-xs text-neutral-500 font-semibold">
                                                                <UpRightAndDownLeftFromCenterIcon className="size-3"/>
                                                            </Button>
                                                            <Button variant="ghost" size="sm"
                                                                    asChild
                                                                    className="p-1 rounded-full h-6 text-xs text-neutral-500 font-semibold">
                                                                <a href={o.link} download={o.name}
                                                                   target='_blank'>
                                                                    <DownloadIcon className="size-3"/>
                                                                </a>
                                                            </Button>
                                                            {canDelete && <Button variant="ghost"
                                                                                        onClick={() => doDelete(o)}
                                                                                        className="p-1 rounded-full h-6 text-xs text-neutral-500 font-semibold">
                                                                <TrashIcon className="size-3"/>
                                                            </Button>}
                                                        </div>
                                                    </div>

                                                </div>

                                            </div>
                                        })}
                                    </div>
                                </DropdownMenuGroup>
                            </>}
                        </div>
                    </div>
                </DropdownMenuContent>
            </DropdownMenu>}
        </div>
    </>
}

export const FileHeaderDropDownMore = (props: HeaderEditDropDownOptionsProps) => {
    const column = props.column as FilesColumn
    const isMulti = column.isMulti
    return (
        <>
            <DropdownMenuSub>
                <DropdownMenuSubTrigger className="text-xs rounded-none p-2">
                    Allow Multiple
                </DropdownMenuSubTrigger>
                <DropdownMenuPortal>
                    <DropdownMenuSubContent className="rounded-none font-medium">
                        <DropdownMenuItem
                            onClick={() => props.onUpdate({isMulti: !isMulti})}
                            className="text-xs rounded-none p-2">
                            <span className="flex-1 capitalize">Yes</span>
                            {isMulti && <FACheckIcon className="size-3"/>}
                        </DropdownMenuItem>
                        <DropdownMenuItem
                            onClick={() => props.onUpdate({isMulti: !isMulti})}
                            className="text-xs rounded-none p-2">
                            <span className="flex-1 capitalize">No</span>
                            {!isMulti && <FACheckIcon className="size-3"/>}
                        </DropdownMenuItem>
                    </DropdownMenuSubContent>
                </DropdownMenuPortal>
            </DropdownMenuSub>
        </>
    )
}