"use client";

import React, { createContext, useContext, useState, useCallback, useMemo, PropsWithChildren } from 'react'

export interface PeekItem {
    id: string
    recordId: string
    databaseId: string
    level: number
}

interface PeekStackContextProps {
    peekStack: PeekItem[]
    pushPeek: (recordId: string, databaseId: string) => string
    popPeek: (id?: string) => void
    clearPeekStack: () => void
    getTopPeek: () => PeekItem | null
    getPeekById: (id: string) => PeekItem | null
    isInPeekContext: boolean
    getCurrentLevel: () => number
}

export const PeekStackContext = createContext<PeekStackContextProps | null>(null)

export const PeekStackProvider = ({ children }: PropsWithChildren) => {
    const [peekStack, setPeekStack] = useState<PeekItem[]>([])

    const pushPeek = useCallback((recordId: string, databaseId: string) => {
        const id = `peek-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
        const level = peekStack.length
        const newPeek: PeekItem = {
            id,
            recordId,
            databaseId,
            level
        }
        setPeekStack(prev => [...prev, newPeek])
        return id
    }, [peekStack.length])

    const popPeek = useCallback((id?: string) => {
        setPeekStack(prev => {
            if (id) {
                // Remove specific peek and all peeks above it
                const targetIndex = prev.findIndex(item => item.id === id)
                if (targetIndex === -1) return prev
                return prev.slice(0, targetIndex)
            } else {
                // Remove top peek
                return prev.slice(0, -1)
            }
        })
    }, [])

    const clearPeekStack = useCallback(() => {
        setPeekStack([])
    }, [])

    const getTopPeek = useCallback(() => {
        return peekStack.length > 0 ? peekStack[peekStack.length - 1] : null
    }, [peekStack])

    const getPeekById = useCallback((id: string) => {
        return peekStack.find(item => item.id === id) || null
    }, [peekStack])

    const getCurrentLevel = useCallback(() => {
        return peekStack.length
    }, [peekStack.length])

    const isInPeekContext = peekStack.length > 0

    const contextValue = useMemo(() => ({
        peekStack,
        pushPeek,
        popPeek,
        clearPeekStack,
        getTopPeek,
        getPeekById,
        isInPeekContext,
        getCurrentLevel,
    }), [peekStack, pushPeek, popPeek, clearPeekStack, getTopPeek, getPeekById, isInPeekContext, getCurrentLevel])

    return (
        <PeekStackContext.Provider value={contextValue}>
            {children}
        </PeekStackContext.Provider>
    )
}

export const usePeekStack = () => {
    const context = useContext(PeekStackContext)
    if (!context) {
        throw new Error('usePeekStack must be used within a PeekStackProvider')
    }
    return context
} 










// "use client";

// import React, { createContext, useContext, useState, useCallback, useMemo, PropsWithChildren } from 'react'

// export interface PeekItem {
//     id: string
//     recordId: string
//     databaseId: string
//     level: number
// }

// interface PeekStackContextProps {
//     peekStack: PeekItem[]
//     pushPeek: (recordId: string, databaseId: string) => string
//     popPeek: (id?: string) => void
//     clearPeekStack: () => void
//     getTopPeek: () => PeekItem | null
//     getPeekById: (id: string) => PeekItem | null
//     isInPeekContext: boolean
//     getCurrentLevel: () => number
// }

// export const PeekStackContext = createContext<PeekStackContextProps | null>(null)

// export const PeekStackProvider = ({ children }: PropsWithChildren) => {
//     const [peekStack, setPeekStack] = useState<PeekItem[]>([])

//     const pushPeek = useCallback((recordId: string, databaseId: string) => {
//         const id = `peek-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
//         const level = peekStack.length
//         const newPeek: PeekItem = {
//             id,
//             recordId,
//             databaseId,
//             level
//         }

//         setPeekStack(prev => {
//             const newStack = [...prev, newPeek];
//             return newStack;
//         });
//         return id
//     }, [peekStack.length])

//     const popPeek = useCallback((id?: string) => {
//         setPeekStack(prev => {
//             if (id) {
//                 // Remove specific peek and all peeks above it
//                 const targetIndex = prev.findIndex(item => item.id === id)
//                 if (targetIndex === -1) return prev
//                 return prev.slice(0, targetIndex)
//             } else {
//                 // Remove top peek
//                 return prev.slice(0, -1)
//             }
//         })
//     }, [])

//     const clearPeekStack = useCallback(() => {
//         setPeekStack([])
//     }, [])

//     const getTopPeek = useCallback(() => {
//         return peekStack.length > 0 ? peekStack[peekStack.length - 1] : null
//     }, [peekStack])

//     const getPeekById = useCallback((id: string) => {
//         return peekStack.find(item => item.id === id) || null
//     }, [peekStack])

//     const getCurrentLevel = useCallback(() => {
//         return peekStack.length
//     }, [peekStack.length])

//     const isInPeekContext = peekStack.length > 0

//     const contextValue = useMemo(() => ({
//         peekStack,
//         pushPeek,
//         popPeek,
//         clearPeekStack,
//         getTopPeek,
//         getPeekById,
//         isInPeekContext,
//         getCurrentLevel,
//     }), [peekStack, pushPeek, popPeek, clearPeekStack, getTopPeek, getPeekById, isInPeekContext, getCurrentLevel])

//     return (
//         <PeekStackContext.Provider value={contextValue}>
//             {children}
//         </PeekStackContext.Provider>
//     )
// }

// export const usePeekStack = () => {
//     const context = useContext(PeekStackContext)
//     if (!context) {
//         throw new Error('usePeekStack must be used within a PeekStackProvider')
//     }
//     return context
// } 