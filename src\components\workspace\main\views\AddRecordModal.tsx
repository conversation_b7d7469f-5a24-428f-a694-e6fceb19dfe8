"use client";

import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useViews } from "@/providers/views";
import { useWorkspace } from "@/providers/workspace";
import { useMaybeRecord } from "@/providers/record";
import { DbRecordFilter, DatabaseFieldDataType, RecordValues, DatabaseColumn } from "opendb-app-db-utils/lib/typings/db";
import { FormFieldBody } from "@/components/workspace/main/views/form/components/common/formFieldBody";
import { AccessLevel } from "@/typings/page";
import { usePage } from "@/providers/page";
import { Database } from "@/typings/database";

interface AddRecordModalProps {
    open: boolean;
    onClose: () => void;
    databaseId: string;
    viewFilter?: DbRecordFilter;
    contextualFilter?: DbRecordFilter;
    onRecordCreated?: (recordId: string) => void;
}

interface FilterAnalysis {
    prePopulatedValues: RecordValues;
    requiredFields: DatabaseColumn[];
    conflictingFields: string[]; // Fields with negative filters that can't be pre-populated
}

export const AddRecordModal = ({ 
    open, 
    onClose, 
    databaseId, 
    viewFilter, 
    contextualFilter,
    onRecordCreated 
}: AddRecordModalProps) => {
    const { createRecords } = useViews();
    const { databaseStore } = useWorkspace();
    const { accessLevel } = usePage();
    const maybeRecord = useMaybeRecord();
    
    const [recordValues, setRecordValues] = useState<RecordValues>({});
    const [isCreating, setIsCreating] = useState(false);
    const [analysis, setAnalysis] = useState<FilterAnalysis | null>(null);

    const database = databaseStore[databaseId];
    const canEdit = accessLevel && [AccessLevel.Full, AccessLevel.Edit].includes(accessLevel);

    // Analyze filters and pre-populate values
    useEffect(() => {
        if (!database || !open) return;

        const analysis = analyzeFilters(
            database.database, 
            viewFilter, 
            contextualFilter, 
            maybeRecord?.recordInfo.record.id
        );
        
        setAnalysis(analysis);
        setRecordValues(analysis.prePopulatedValues);
    }, [database, viewFilter, contextualFilter, maybeRecord, open]);

    const handleCreate = async () => {
        if (!canEdit || !database) return;

        setIsCreating(true);
        try {
            const result = await createRecords(databaseId, [recordValues]);
            if (result && result.records && result.records.length > 0) {
                onRecordCreated?.(result.records[0].id);
                onClose();
            }
        } catch (error) {
            console.error('Failed to create record:', error);
        } finally {
            setIsCreating(false);
        }
    };

    const handleFieldChange = (columnId: string, value: any) => {
        setRecordValues(prev => ({
            ...prev,
            [columnId]: value
        }));
    };

    if (!database || !analysis) return null;

    const requiredFieldsToShow = analysis.requiredFields.filter(field => 
        !analysis.prePopulatedValues.hasOwnProperty(field.id)
    );

    const hasRequiredFields = requiredFieldsToShow.length > 0;
    const hasConflicts = analysis.conflictingFields.length > 0;

    return (
        <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
            <DialogContent className="max-w-2xl max-h-[90vh] !rounded-none p-0" hideCloseBtn>
                <DialogHeader className="p-4 border-b flex flex-row items-center justify-between">
                    <div className="flex items-center gap-2">
                        <DialogTitle className="text-sm font-semibold">Add New Record</DialogTitle>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button
                            variant="outline"
                            size="sm"
                            className="text-xs rounded-full"
                            onClick={onClose}
                            disabled={isCreating}
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleCreate}
                            size="sm"
                            className="text-xs rounded-full bg-black text-white hover:bg-gray-800"
                            disabled={isCreating || !canEdit}
                        >
                            {isCreating ? 'Creating...' : 'Create'}
                        </Button>
                    </div>
                </DialogHeader>

                <div className="max-h-[calc(90vh-120px)] overflow-auto mention-input-container">
                    <div className="p-4 space-y-6">
                        {/* Description */}
                        {(Object.keys(analysis.prePopulatedValues).length > 0 || hasConflicts) && (
                            <div className="text-sm text-gray-600">
                                {Object.keys(analysis.prePopulatedValues).length > 0 && (
                                    <div className="text-green-600 mb-1">
                                        Some fields have been pre-populated based on current filters.
                                    </div>
                                )}
                                {hasConflicts && (
                                    <div className="text-orange-600">
                                        Some filters couldn't be used for pre-population and will need manual input.
                                    </div>
                                )}
                            </div>
                        )}

                        {/* Show pre-populated fields (read-only) */}
                        {Object.keys(analysis.prePopulatedValues).length > 0 && (
                            <div className="border border-green-200 rounded-lg">
                                <div className="px-3 py-2 border-b border-green-200 bg-green-50">
                                    <h4 className="text-sm font-semibold text-green-700">Pre-populated Fields</h4>
                                </div>
                                <div className="p-3 space-y-3">
                                    {Object.entries(analysis.prePopulatedValues).map(([columnId, value]) => {
                                        const column = database.database.definition.columnsMap[columnId];
                                        if (!column) return null;

                                        return (
                                            <div key={columnId} className="flex justify-between items-center">
                                                <span className="text-sm font-medium text-gray-900">{column.title}</span>
                                                <span className="text-sm text-gray-600">
                                                    {formatFieldValue(value, column)}
                                                </span>
                                            </div>
                                        );
                                    })}
                                </div>
                            </div>
                        )}

                        {/* Show required fields that need manual input */}
                        {hasRequiredFields && (
                            <div className="border border-gray-200 rounded-lg">
                                <div className="px-3 py-2 border-b border-gray-200 bg-gray-50">
                                    <h4 className="text-sm font-semibold text-gray-900">Required Fields</h4>
                                </div>
                                <div className="p-3 space-y-4">
                                    {requiredFieldsToShow.map((field) => (
                                        <div key={field.id}>
                                            <FormFieldBody
                                                id={field.id}
                                                values={recordValues}
                                                updateValues={setRecordValues}
                                                columnsMap={database.database.definition.columnsMap}
                                                columnsPropMap={{}}
                                                databaseId={databaseId}
                                                disabled={!canEdit}
                                                isEditing
                                                activeField={''}
                                                setActiveField={() => {}}
                                                updateFieldProps={() => {}}
                                            />
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* Show conflicting fields */}
                        {hasConflicts && (
                            <div className="border border-orange-200 rounded-lg">
                                <div className="px-3 py-2 border-b border-orange-200 bg-orange-50">
                                    <h4 className="text-sm font-semibold text-orange-700">Fields Requiring Input</h4>
                                </div>
                                <div className="p-3">
                                    <div className="text-sm text-gray-600">
                                        The following fields have filters that prevent pre-population: {analysis.conflictingFields.join(', ')}
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
};

// Utility function to analyze filters and determine pre-population values
function analyzeFilters(
    database: Database, 
    viewFilter?: DbRecordFilter, 
    contextualFilter?: DbRecordFilter,
    currentRecordId?: string
): FilterAnalysis {
    const prePopulatedValues: RecordValues = {};
    const requiredFields: DatabaseColumn[] = [];
    const conflictingFields: string[] = [];

    // Get all required fields
    Object.values(database.definition.columnsMap).forEach(column => {
        if (column.type === DatabaseFieldDataType.Text) {
            requiredFields.push(column);
        }
    });

    // Analyze view filter
    if (viewFilter?.conditions) {
        analyzeFilterConditions(
            viewFilter.conditions, 
            database, 
            prePopulatedValues, 
            conflictingFields,
            currentRecordId
        );
    }

    // Analyze contextual filter
    if (contextualFilter?.conditions) {
        analyzeFilterConditions(
            contextualFilter.conditions, 
            database, 
            prePopulatedValues, 
            conflictingFields,
            currentRecordId
        );
    }

    return {
        prePopulatedValues,
        requiredFields,
        conflictingFields: Array.from(new Set(conflictingFields))
    };
}

function analyzeFilterConditions(
    conditions: any[], 
    database: Database, 
    prePopulatedValues: RecordValues, 
    conflictingFields: string[],
    currentRecordId?: string
) {
    conditions.forEach(condition => {
        const column = database.definition.columnsMap[condition.columnId];
        if (!column) return;

        // Skip if already processed
        if (prePopulatedValues.hasOwnProperty(condition.columnId)) return;

        // Handle positive equality filters
        if (condition.op === 'equals' || condition.op === 'is') {
            let value = condition.value;
            
            // Handle "current_record" magic value
            if (value === 'current_record' && currentRecordId) {
                value = currentRecordId;
            } else if (Array.isArray(value) && value.includes('current_record') && currentRecordId) {
                value = value.map(v => v === 'current_record' ? currentRecordId : v);
            }

            // Only pre-populate if it's a simple, positive value
            if (value && value !== 'current_record') {
                prePopulatedValues[condition.columnId] = value;
            }
        } else {
            // Negative or complex filters can't be pre-populated
            conflictingFields.push(column.title);
        }
    });
}

function formatFieldValue(value: any, column: DatabaseColumn): string {
    if (value === null || value === undefined) return 'Empty';
    
    switch (column.type) {
        case DatabaseFieldDataType.Text:
            return String(value);
        case DatabaseFieldDataType.Number:
            return String(value);
        case DatabaseFieldDataType.Checkbox:
            return value ? 'Yes' : 'No';
        case DatabaseFieldDataType.Select:
            return String(value);
        case DatabaseFieldDataType.Date:
            return new Date(value).toLocaleDateString();
        case DatabaseFieldDataType.Linked:
            return Array.isArray(value) ? `${value.length} items` : '1 item';
        default:
            return String(value);
    }
} 