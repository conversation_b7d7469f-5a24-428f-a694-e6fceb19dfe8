"use client";
import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/custom-ui/select";
import { ButtonAction, ActionButton } from "opendb-app-db-utils/lib/typings/db";
import { TrashIcon, PlusIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { CircleExclamationIcon } from "@/components/icons/FontAwesomeRegular";
import { getActionIcon } from "../buttonGroup";
import { generateUUID } from "opendb-app-db-utils/lib/methods/string";
import { useWorkspace } from "@/providers/workspace";
import { useAuth } from "@/providers/user";
import { DNDSortable, SortItem } from "@/components/custom-ui/dndSortable";
import {  getButtonActionDefinition,  getAvailableActions,  getDefaultPropsForActionType,isActionConfigReady } from "@/utils/buttonActionHelpers";
import { ReusableFieldRenderers, getRendererForFieldType } from "@/components/workspace/main/common/FieldRenderer";
import { DatabaseColumnValueMapper } from "@/components/workspace/main/common/DatabaseColumnValueMapper";
import { DbCondition } from "opendb-app-db-utils/lib/typings/db";
import { CompareOperator } from "opendb-app-db-utils/lib/methods/compare";
import { CompareOperatorSelect, SingleCompare } from "@/components/custom-ui/compareOperatorSelect";
import { MentionInput } from "@/components/custom-ui/mentionInput";
import { ViewFilter } from "@/components/workspace/main/views/common/viewFilter";
import { DbRecordFilter, Match } from "opendb-app-db-utils/lib/typings/db";
import { getWorkspaceSenders } from "@/api/workspace";
import { WorkspaceSenderEmail } from "@/typings/workspace";
import { getWorkflows } from "@/api/workflow";
import { Workflow, WorkflowTriggerType } from "@/typings/workflow";
import { UpdateRecordEditor } from "@/components/workspace/main/common/UpdateRecordEditor";
import { CustomSelect } from "@/components/custom-ui/customSelect";
import { InputWithEnter } from "@/components/custom-ui/inputWithEnter";


// Interface for the ActionConfigEditor props
export interface ActionConfigEditorProps {
    action: ButtonAction;
    onUpdate: (updatedAction: ButtonAction) => void;
    onDelete: () => void;
    databaseId: string;
    workspaceSenders: WorkspaceSenderEmail[];
    workflows: Workflow[];
}

export const ActionConfigEditor = ({ action, onUpdate, onDelete, databaseId, workspaceSenders, workflows }: ActionConfigEditorProps) => {
    const { databaseStore } = useWorkspace();

    // Initialize state with action values
    const [actionType, setActionType] = useState<ButtonAction['actionType']>(action.actionType || "openUrl");
    const [props, setProps] = useState<any>(action.props || getDefaultPropsForActionType(action.actionType || "openUrl"));
    const [isExpanded, setIsExpanded] = useState<boolean>(true);

    const buttonAction = getButtonActionDefinition(actionType);
    const availableActions = getAvailableActions();

    // Check if action is ready (all required props filled)
    const isActionReady = () => {
        return isActionConfigReady({ actionType, props });
    };

    const variableKeyMap: Record<string, { label: string; description: string; tag: string }> = {};
    if (databaseId && databaseStore?.[databaseId]?.database?.definition?.columnsMap) {
        const columnsMap = databaseStore[databaseId].database.definition.columnsMap;
        Object.entries(columnsMap).forEach(([columnId, column]) => {
            variableKeyMap[`column.${columnId}`] = {
                label: `Column: ${column.title}`,
                description: `Column value for ${column.title}`,
                tag: `{{column.${columnId}}}`
            };
        });
    }

    variableKeyMap['person.current'] = { label: 'Current User', description: 'Current user ID', tag: '{{person.current}}' };
    variableKeyMap['person.email'] = { label: 'Current User Email', description: 'Current user email address', tag: '{{person.email}}' };
    variableKeyMap['person.name'] = { label: 'Current User Name', description: 'Current user name', tag: '{{person.name}}' };

    const updateProp = (key: string, value: unknown) => {
        const newProps = { ...props, [key]: value };
        setProps(newProps);
        
        const updatedAction: ButtonAction = {
            ...action,
            actionType: actionType,
            props: newProps
        };
        onUpdate(updatedAction);
    };

    const handleActionTypeChange = (value: string) => {
        const newActionType = value as ButtonAction['actionType'];
        setActionType(newActionType);

        let newProps = getDefaultPropsForActionType(newActionType);
        if (newActionType === 'updateRecord') {
            newProps.updates = [];
        }

        // Update local state
        setProps(newProps);

        const updatedAction = {
            ...action,
            actionType: newActionType,
            props: newProps
        } as unknown as ButtonAction;

        onUpdate(updatedAction);
    };

    if (!buttonAction) {
        return null;
    }

    return (
        <div className="border rounded-md mb-3 bg-white overflow-visible">
            <div
                className={`flex items-center justify-between p-3 ${!isExpanded ? 'cursor-pointer hover:bg-gray-50' : ''}`}
                onClick={!isExpanded ? () => setIsExpanded(true) : undefined}
            >
                <div className="flex items-center gap-2">
                    {!isActionReady() && (
                        <CircleExclamationIcon className="size-3 text-destructive inline-block" />
                    )}
                    {getActionIcon(actionType)}
                    {isExpanded ? (
                        <Select value={actionType} onValueChange={handleActionTypeChange}>
                            <SelectTrigger className="h-8 border border-solid rounded-md w-40 text-xs">
                                <SelectValue placeholder="Select action type" />
                            </SelectTrigger>
                            <SelectContent>
                                {availableActions.map(({ value, label }) => (
                                    <SelectItem key={value} value={value}>{label}</SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    ) : (
                        <span className="text-sm font-medium">
                            {availableActions.find(a => a.value === actionType)?.label || actionType}
                        </span>
                    )}
                </div>
                <div className="flex items-center gap-2">
                    {isExpanded && (
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setIsExpanded(false)}
                            className="h-8 text-xs border border-solid rounded-md"
                        >
                            Done
                        </Button>
                    )}
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={onDelete}
                        className="p-1 h-6 w-6 hover:bg-gray-50 text-gray-700"
                    >
                        <TrashIcon className="h-3 w-3" />
                    </Button>
                </div>
            </div>

            {isExpanded && (

            <div className="space-y-3 mt-3 p-3">
                {Object.entries(buttonAction.props || {}).map(([key, prop]) => {
                    if (key === 'databaseId') {
                        const dbOptions = Object.entries(databaseStore || {}).map(([dbId, dbData]) => ({
                            id: dbId,
                            value: dbId,
                            title: dbData.database?.name || dbId,
                            data: dbData.database || {},
                        }));
                        return (
                            <div key={key} className="space-y-1">
                                <Label className="text-xs font-medium">{prop.displayName}</Label>
                                <CustomSelect
                                    selectedIds={[props[key] || databaseId]}
                                    options={dbOptions}
                                    onChange={(selected) => updateProp(key, selected[0])}
                                    placeholder="Select database"
                                    className="text-xs"
                                />
                                {prop.description && (
                                    <p className="text-xs text-gray-500">{prop.description}</p>
                                )}
                            </div>
                        );
                    }

                    if (key === 'workflowId') {
                        const workflowOptions = workflows.map((workflow) => ({
                            id: workflow.id.toString(),
                            value: workflow.id.toString(),
                            title: workflow.name || `Workflow ${workflow.id}`,
                            data: workflow,
                        }));
                        return (
                            <div key={key} className="space-y-1">
                                <Label className="text-xs font-medium">{prop.displayName}</Label>
                                <CustomSelect
                                    selectedIds={[props[key]]}
                                    options={workflowOptions}
                                    onChange={(selected) => updateProp(key, selected[0])}
                                    placeholder="Select workflow"
                                    className="text-xs"
                                />
                                {prop.description && (
                                    <p className="text-xs text-gray-500">{prop.description}</p>
                                )}
                            </div>
                        );
                    }

                    if (key === 'senderId') {
                        const senderOptions = workspaceSenders.map((sender) => ({
                            id: sender.id.toString(),
                            value: sender.id.toString(),
                            title: `${sender.name} (${sender.email})${sender.isVerified ? ' ✓' : ' (Unverified)'}`,
                            data: sender,
                        }));
                        return (
                            <div key={key} className="space-y-1">
                                <Label className="text-xs font-medium">{prop.displayName}</Label>
                                <CustomSelect
                                    selectedIds={[props[key]]}
                                    options={senderOptions}
                                    onChange={(selected) => updateProp(key, selected[0])}
                                    placeholder="Select sender"
                                    className="text-xs"
                                />
                                {prop.description && (
                                    <p className="text-xs text-gray-500">{prop.description}</p>
                                )}
                            </div>
                        );
                    }

                    if (actionType === 'updateRecord' && key === 'updates') {
                        return (
                            <div key={key} className="space-y-1">
                                <Label className="text-xs font-medium">{prop.displayName}</Label>
                                <UpdateRecordEditor
                                    databaseId={props.databaseId || databaseId}
                                    updates={props[key] || []}
                                    onUpdate={(newUpdates) => updateProp(key, newUpdates)}
                                    variableKeyMap={variableKeyMap}
                                />
                                {prop.description && (
                                    <p className="text-xs text-gray-500">{prop.description}</p>
                                )}
                            </div>
                        )
                    }

                    const FieldRenderer = getRendererForFieldType(prop.type);
                    if (FieldRenderer) {
                        return (
                            <FieldRenderer
                                key={key}
                                value={props[key]}
                                onChange={(value: unknown) => updateProp(key, value)}
                                propKey={key}
                                prop={prop}
                                disabled={false}
                                tagOptionsMap={variableKeyMap}
                            />
                        );
                    }

                    return (
                        <div key={key} className="text-xs text-gray-500">
                            Unsupported field type: {prop.type}
                        </div>
                    );
                })}
            </div>
            )}
        </div>
    );
};

interface ConditionEditorProps {
    condition: DbCondition;
    databaseId: string;
    onChange: (condition: DbCondition) => void;
    onDelete: () => void;
    variableKeyMap: Record<string, { label: string; description: string; tag: string }>;
}

const ConditionEditor: React.FC<ConditionEditorProps> = ({
    condition,
    databaseId,
    onChange,
    onDelete,
    variableKeyMap
}) => {
    const { databaseStore } = useWorkspace();

    const database = databaseStore?.[databaseId];
    const columns = database?.database?.definition?.columnsMap || {};
    const column = columns[condition.columnId];

    return (
        <div className="flex items-center gap-2 p-2 border rounded-md bg-gray-50">
            <div className="w-32">
                <MentionInput
                    keyMap={variableKeyMap}
                    value={condition.columnId}
                    onChange={(value) => onChange({ ...condition, columnId: value })}
                    placeholder="Field"
                    className="text-xs"
                />
            </div>

            <div className="w-32">
                <CompareOperatorSelect
                    value={condition.op}
                    onChange={(op) => onChange({ ...condition, op })}
                    fieldType={column?.type}
                    className="text-xs"
                />
            </div>

            {!SingleCompare.includes(condition.op) && (
                <div className="flex-1">
                    <MentionInput
                        keyMap={variableKeyMap}
                        value={Array.isArray(condition.value) ? condition.value.join(', ') : (condition.value || '')}
                        onChange={(value) => onChange({ ...condition, value })}
                        placeholder="Value"
                        className="text-xs"
                    />
                </div>
            )}

            <Button
                variant="ghost"
                size="sm"
                onClick={onDelete}
                className="p-1 h-6 w-6 hover:bg-red-50 text-red-600"
            >
                <TrashIcon className="h-3 w-3" />
            </Button>
        </div>
    );
};

export interface ButtonEditorProps {
    initialValue?: ActionButton;
    button?: ActionButton;
    onSave: (value: ActionButton) => void;
    onUpdate?: (value: ActionButton) => void;
    onCancel: () => void;
    databaseId: string;
    contextIsRecord?: boolean;
}

export const ButtonEditor = React.forwardRef(({
    initialValue,
    button,
    onSave,
    onUpdate,
    onCancel,
    databaseId,
    contextIsRecord = false,
}: ButtonEditorProps, ref) => {
    const buttonData = initialValue || button;
    const saveHandler = onSave || onUpdate;
    const { databaseStore, workspace } = useWorkspace();
    const { token } = useAuth();


    const [label, setLabel] = useState<string>(buttonData?.label || "");
    const [actions, setActions] = useState<ButtonAction[]>(buttonData?.actions || []);
    const [visibleIfConditions, setVisibleIfConditions] = useState<DbCondition[]>((buttonData as { visibleIf?: DbCondition[]; visibleIfConditions?: DbCondition[] })?.visibleIf || (buttonData as { visibleIf?: DbCondition[]; visibleIfConditions?: DbCondition[] })?.visibleIfConditions || []);
    const [enabledIfConditions, setEnabledIfConditions] = useState<DbCondition[]>((buttonData as { enabledIf?: DbCondition[]; enabledIfConditions?: DbCondition[] })?.enabledIf || (buttonData as { enabledIf?: DbCondition[]; enabledIfConditions?: DbCondition[] })?.enabledIfConditions || []);
    const [visibleIfFilter, setVisibleIfFilter] = useState<DbRecordFilter>((buttonData as { visibleIfFilter?: DbRecordFilter })?.visibleIfFilter || { conditions: [], match: Match.All });
    const [enabledIfFilter, setEnabledIfFilter] = useState<DbRecordFilter>((buttonData as { enabledIfFilter?: DbRecordFilter })?.enabledIfFilter || { conditions: [], match: Match.All });
    const [workspaceSenders, setWorkspaceSenders] = useState<WorkspaceSenderEmail[]>([]);
    const [workflows, setWorkflows] = useState<Workflow[]>([]);

    // Load workspace senders
    useEffect(() => {
        const loadSenders = async () => {
            if (!token || !workspace) return;
            try {
                const response = await getWorkspaceSenders(token.token, workspace.workspace.id);
                if (response.data?.data?.senders) {
                    setWorkspaceSenders(response.data.data.senders);
                }
            } catch (error) {
                console.error('Failed to load workspace senders:', error);
            }
        };
        loadSenders();
    }, [token, workspace]);

    // Load workflows
    useEffect(() => {
        const loadWorkflows = async () => {
            if (!token || !workspace) return;
            try {
                const response = await getWorkflows(token.token, workspace.workspace.id, {
                    triggerTypes: [WorkflowTriggerType.OnDemand_Callable]
                });
                if (response.data?.data?.workflows) {
                    setWorkflows(response.data.data.workflows);
                }
            } catch (error) {
                console.error('Failed to load workflows:', error);
            }
        };
        loadWorkflows();
    }, [token, workspace]);


    const handleSave = () => {
        if (!saveHandler) return;
        const finalButton = {
            ...(buttonData as ActionButton),
            id: buttonData?.id || generateUUID(),
            label,
            actions,
            visibleIf: visibleIfConditions,
            enabledIf: enabledIfConditions,
            visibleIfFilter: visibleIfFilter,
            enabledIfFilter: enabledIfFilter,
            isReady: true
        } as ActionButton;
        saveHandler(finalButton);
    };

    React.useImperativeHandle(ref, () => ({
        save: handleSave,
    }));

    // Create variable key map for mention inputs
    const variableKeyMap: Record<string, { label: string; description: string; tag: string }> = {};
    if (databaseId && databaseStore?.[databaseId]?.database?.definition?.columnsMap) {
        const columnsMap = databaseStore[databaseId].database.definition.columnsMap;
        Object.entries(columnsMap).forEach(([columnId, column]) => {
            variableKeyMap[`column.${columnId}`] = {
                label: `Column: ${column.title}`,
                description: `Column value for ${column.title}`,
                tag: `{{column.${columnId}}}`
            };
        });
    }

    variableKeyMap['person.current'] = { label: 'Current User', description: 'Current user ID', tag: '{{person.current}}' };
    variableKeyMap['person.email'] = { label: 'Current User Email', description: 'Current user email address', tag: '{{person.email}}' };
    variableKeyMap['person.name'] = { label: 'Current User Name', description: 'Current user name', tag: '{{person.name}}' };

    const addAction = () => {
        const newAction: ButtonAction = {
            id: generateUUID(),
            label: "New Action",
            isReady: true,
            actionType: "openUrl",
            props: getDefaultPropsForActionType("openUrl") as unknown as ButtonAction['props']
        };
        setActions([...actions, newAction]);
    };

    const updateAction = (index: number, updatedAction: ButtonAction) => {
        const newActions = [...actions];
        newActions[index] = updatedAction;
        setActions(newActions);
    };

    const deleteAction = (index: number) => {
        const newActions = [...actions];
        newActions.splice(index, 1);
        setActions(newActions);
    };

    const reorderActions = (items: SortItem<ButtonAction>[]) => {
        setActions(items.map(item => item.data));
    };

    return (
        <div className="space-y-4 p-4 overflow-visible">
            <div className="space-y-4">
                <div className="space-y-2">
                    <Label className="text-sm font-medium">Button Label</Label>
                    <InputWithEnter
                        value={label}
                        onChange={(value) => setLabel(value)}
                        placeHolder="New Button"
                        wrapperClassname="text-sm rounded-full"
                        shortEnter={true}
                    />
                </div>

                <div className="space-y-2">
                    <Label className="text-sm font-medium">When</Label>
                    <div className="p-3 border rounded-md bg-gray-50">
                        <span className="text-sm text-gray-700">Button is clicked</span>
                    </div>
                </div>

                <div className="space-y-2">
                    <Label className="text-sm font-medium">Do</Label>

                {actions.length > 0 ? (
                    <DNDSortable
                        items={actions.map(action => ({ id: action.id, data: action }))}
                        itemRenderer={(index, item) => (
                            <ActionConfigEditor
                                action={item.data}
                                databaseId={databaseId}
                                workspaceSenders={workspaceSenders}
                                workflows={workflows}
                                onUpdate={(updatedAction) => updateAction(index, updatedAction)}
                                onDelete={() => deleteAction(index)}
                            />
                        )}
                        onChange={reorderActions}
                        useDragHandle
                        handlePosition="center"
                    />
                ) : (
                    <div className="text-gray-400 text-center py-8 text-sm">
                        No actions configured
                    </div>
                )}

                <Button
                    variant="ghost"
                    size="sm"
                    onClick={addAction}
                    className="text-xs mt-4 w-full text-gray-600 hover:text-gray-800"
                >
                    <PlusIcon className="h-4 w-4 mr-1" />
                    <span>New action</span>
                </Button>
            </div>

            <div className="border-t pt-4">
                <h3 className="text-sm font-semibold mb-4">Conditions</h3>
                <div className="mb-4">
                    <Label className="text-xs text-gray-600 mb-2 block">Show this button when:</Label>
                    <div className="space-y-2">
                        {visibleIfConditions.map((condition, index) => (
                            <ConditionEditor
                                key={index}
                                condition={condition}
                                databaseId={databaseId}
                                variableKeyMap={variableKeyMap}
                                onChange={(updatedCondition) => {
                                    const newConditions = [...visibleIfConditions];
                                    newConditions[index] = updatedCondition;
                                    setVisibleIfConditions(newConditions);
                                }}
                                onDelete={() => {
                                    const newConditions = [...visibleIfConditions];
                                    newConditions.splice(index, 1);
                                    setVisibleIfConditions(newConditions);
                                }}
                            />
                        ))}
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setVisibleIfConditions([...visibleIfConditions, { columnId: '', op: CompareOperator.Equals, value: '' }])}
                            className="text-xs rounded-full w-full text-gray-600 hover:text-gray-800"
                        >
                            + Add visibility condition
                        </Button>

                        {/* Add database filter option for record context */}
                        {contextIsRecord && (
                            <div className="mt-3 pt-3 border-t border-gray-200">
                                <Label className="text-xs text-gray-600 mb-2 block">And when record matches the filter:</Label>
                                <ViewFilter
                                    database={databaseStore?.[databaseId]?.database}
                                    filter={visibleIfFilter}
                                    onChange={setVisibleIfFilter}
                                    trigger={
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="text-xs rounded-full w-full text-gray-600 hover:text-gray-800"
                                        >
                                            {visibleIfFilter.conditions.length > 0
                                                ? `${visibleIfFilter.conditions.length} filter${visibleIfFilter.conditions.length > 1 ? 's' : ''} applied`
                                                : '+ Add database filter'
                                            }
                                        </Button>
                                    }
                                    tagOptionsMap={variableKeyMap}
                                />
                            </div>
                        )}
                    </div>
                </div>

                <div className="mb-4">
                    <Label className="text-xs text-gray-600 mb-2 block">Enable this button when:</Label>
                    <div className="space-y-2">
                        {enabledIfConditions.map((condition, index) => (
                            <ConditionEditor
                                key={index}
                                condition={condition}
                                databaseId={databaseId}
                                variableKeyMap={variableKeyMap}
                                onChange={(updatedCondition) => {
                                    const newConditions = [...enabledIfConditions];
                                    newConditions[index] = updatedCondition;
                                    setEnabledIfConditions(newConditions);
                                }}
                                onDelete={() => {
                                    const newConditions = [...enabledIfConditions];
                                    newConditions.splice(index, 1);
                                    setEnabledIfConditions(newConditions);
                                }}
                            />
                        ))}
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setEnabledIfConditions([...enabledIfConditions, { columnId: '', op: CompareOperator.Equals, value: '' }])}
                            className="text-xs rounded-full w-full text-gray-600 hover:text-gray-800"
                        >
                            + Add enabled condition
                        </Button>

                        {/* Add database filter option for record context */}
                        {contextIsRecord && (
                            <div className="mt-3 pt-3 border-t border-gray-200">
                                <Label className="text-xs text-gray-600 mb-2 block">And when record matches the filter:</Label>
                                <ViewFilter
                                    database={databaseStore?.[databaseId]?.database}
                                    filter={enabledIfFilter}
                                    onChange={setEnabledIfFilter}
                                    trigger={
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="text-xs rounded-full w-full text-gray-600 hover:text-gray-800"
                                        >
                                            {enabledIfFilter.conditions.length > 0
                                                ? `${enabledIfFilter.conditions.length} filter${enabledIfFilter.conditions.length > 1 ? 's' : ''} applied`
                                                : '+ Add database filter'
                                            }
                                        </Button>
                                    }
                                    tagOptionsMap={variableKeyMap}
                                />
                            </div>
                        )}
                    </div>
                </div>
                </div>
            </div>
        </div>
    );
});

ButtonEditor.displayName = "ButtonEditor";
