import * as ButtonActions from "@/utils/buttonAction";
import { DbCondition, DbRecordFilter, Match } from "opendb-app-db-utils/lib/typings/db";
import { CompareOperator } from "opendb-app-db-utils/lib/methods/compare";
import { SubstituteVarData, substituteVars } from "opendb-app-db-utils/lib/methods/object";
import { useState } from "react";
import { ButtonAction, ButtonState, ButtonActionResult,
  ActionContext, 
  ActionServices,
  User,
  WorkspaceMember,
  WorkspaceData,
  DatabaseData,
  RecordData,
  TokenData
} from './buttonAction';
import { DatabaseFieldDataType } from "opendb-app-db-utils/lib/typings/db";
import { apiUrl } from "@/api/common";

export const resolveActionProps = <T extends object>(
  props: T,
  record: RecordData,
  workspace?: {workspace?: WorkspaceData; id?: string; domain?: string;},
  token?: TokenData,
  user?: User,
  context?: ActionContext
): T => {
  if (!props || typeof props !== 'object') {
    return props;
  }

  const vars: SubstituteVarData = {};
  
  const recordValues = record?.recordValues || 
                      record?.record?.recordValues || 
                      record?.values || 
                      {};
  
  const directValues = record?.record || record || {};
  
  const workspaceId = workspace?.workspace?.id || workspace?.id || context?.workspace?.workspace?.id || context?.workspace?.id;
  
  const dbId = context?.databaseId || context?.database?.id;
  const db = dbId ? context?.databaseStore?.[dbId] : context?.database;
  const columnsMap = db?.definition?.columnsMap || db?.database?.definition?.columnsMap || {};
  
  Object.entries(recordValues).forEach(([key, value]) => {
    if (value !== null && value !== undefined) {
        vars[`column.${key}`] = String(value);
        vars[key] = String(value);
    }
  });

  Object.entries(directValues).forEach(([key, value]) => {
    if (value !== null && value !== undefined && typeof value !== 'object') {
      vars[key] = String(value);
    }
  });
  
  if (workspace) {
    const workspaceData = workspace?.workspace || workspace;
    if (workspaceData && typeof workspaceData === 'object') {
      Object.entries(workspaceData).forEach(([key, value]) => {
        if (value !== null && value !== undefined && typeof value !== 'object') {
          vars[`workspace.${key}`] = String(value);
        }
      });
    }
  }
  
  if (token) {
    Object.entries(token).forEach(([key, value]) => {
      if (value !== null && value !== undefined && typeof value !== 'object') {
        vars[`user.${key}`] = String(value);
      }
    });

    if (token.userId) vars['person.current'] = String(token.userId);
    if (user?.email) vars['person.email'] = user.email;
    if (user?.firstName) vars['person.name'] = user.firstName;
  }
  
  const processValue = (value: unknown, path = ''): unknown => {
    if (typeof value === 'string') {  
      const sanitizedValue = value.replace(/[\u200B-\u200D\uFEFF]/g, '').trim();
      const resolvedValue = substituteVars(sanitizedValue, vars, undefined, 'curly');
      return resolvedValue;
    } else if (Array.isArray(value)) {
      return value.map((item, index) => processValue(item, `${path}[${index}]`));
    } else if (typeof value === 'object' && value !== null) {
      return resolveActionProps(value as object, record, workspace, token, user, context);
    }
    return value;
  };

  const resolved: Record<string, unknown> = {};
  Object.entries(props).forEach(([key, value]) => {
    resolved[key] = processValue(value, key);
  });

  return resolved as T;
};



const evaluateCondition = (
  condition: DbCondition,
  recordValues: Record<string, unknown>
): boolean => {
  let resolvedColumnId = condition.columnId;
  if (condition.columnId.includes('{{') && condition.columnId.includes('}}')) {
    const match = condition.columnId.match(/\{\{column\.([^}]+)\}\}/);
    if (match) {
      resolvedColumnId = match[1];
    }
  }

  const fieldValue = recordValues[resolvedColumnId];
  const compareValue = condition.value;

  let result = false;

  switch (condition.op) {
    case CompareOperator.Equals:
      result = fieldValue === compareValue;
      break;
    case CompareOperator.NotEquals:
      result = fieldValue !== compareValue;
      break;
    case CompareOperator.Contains:
      result = String(fieldValue || '').toLowerCase().includes(String(compareValue || '').toLowerCase());
      break;
    case CompareOperator.DoesNotContain:
      result = !String(fieldValue || '').toLowerCase().includes(String(compareValue || '').toLowerCase());
      break;
    case CompareOperator.StartsWith:
      result = String(fieldValue || '').toLowerCase().startsWith(String(compareValue || '').toLowerCase());
      break;
    case CompareOperator.EndsWith:
      result = String(fieldValue || '').toLowerCase().endsWith(String(compareValue || '').toLowerCase());
      break;
    case CompareOperator.IsEmpty:
      result = !fieldValue || fieldValue === '';
      break;
    case CompareOperator.IsNotEmpty:
      result = !!(fieldValue && fieldValue !== '');
      break;
    case CompareOperator.GreaterThan:
      result = Number(fieldValue) > Number(compareValue);
      break;
    case CompareOperator.LessThan:
      result = Number(fieldValue) < Number(compareValue);
      break;
    case CompareOperator.IsChecked:
      result = fieldValue === true;
      break;
    case CompareOperator.IsNotChecked:
      result = fieldValue !== true;
      break;
    case CompareOperator.IsAnyOf:
      result = Array.isArray(compareValue) && compareValue.includes(String(fieldValue));
      break;
    case CompareOperator.IsNoneOf:
      result = Array.isArray(compareValue) && !compareValue.includes(String(fieldValue));
      break;
    default:
      result = true;
  }

  return result;
}

export const evaluateConditions = (
  conditions: DbCondition[],
  recordValues: Record<string, unknown>
): boolean => {
  if (!conditions || conditions.length === 0) {
    return true;
  }

  const results = conditions.map((condition) => evaluateCondition(condition, recordValues));

  const finalResult = results.every(r => r);
  return finalResult;
};

export const evaluateRecordFilter = (filter: DbRecordFilter | undefined, recordValues: Record<string, unknown>): boolean => {
  if (!filter || !filter.conditions || filter.conditions.length === 0) {
    return true;
  }

  const results = filter.conditions.map(condition => evaluateCondition(condition, recordValues));

  if (filter.match === Match.Any) {
    return results.some(r => r);
  }
  
  return results.every(r => r);
};


export const executeDeclarativeAction = async (
  action: {
    actionType: string;
    props?: Record<string, unknown>;
  },
  context: ActionContext,
  services: ActionServices,
  databaseStore: any,
): Promise<{ success: boolean; result?: any }> => {
  const { actionType, props = {} } = action;


  try {
    const buttonAction = ButtonActions.getButtonAction(actionType);

    if (!buttonAction) {
      const errorMsg = `Unknown action type: ${actionType}`;
      services.toast.error(errorMsg);
      return { success: false };
    }

    const contextWithStore = { ...context, databaseStore, originalActionProps: props };

    const resolvedProps = resolveActionProps(
      action.props || {},
      context.record,
      context.workspace,
      context.token,
      context.user,
      contextWithStore
    );
    
    const result = await buttonAction.run(resolvedProps, contextWithStore, services);
    
    if (actionType === 'showConfirmation' && result && (result as any).confirmed === false) {
      return { success: false, result };
    }
    
    return { success: true, result };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Action failed';

    if (errorMessage === 'Action Cancelled') {
      return { success: false };
    }



    return { success: false };
  }
};


export const isActionConfigReady = (action: { actionType: string; props?: Record<string, unknown> }): boolean => {
    const buttonAction = ButtonActions.getButtonAction(action.actionType);
    if (!buttonAction?.props) return true;

    const props = action.props || {};

    const hasAllRequiredProps = Object.entries(buttonAction.props).every(([key, propDef]) => {
        if (!propDef.required) return true;
        const value = props[key];
        return value !== undefined && value !== null && value !== '';
    });

    if (!hasAllRequiredProps) return false;

    if (action.actionType === 'updateRecord') {
        const updates = (props.updates as any[]) || [];
        if (updates.length === 0) return false; 
        return updates.every(u => u.columnId && u.value !== undefined && u.value !== '');
    }

    return true;
};


export const evaluateButtonState = (
  button: {
    actions?: Array<{actionType: string; props?: Record<string, unknown>}>;
    visibleIf?: DbCondition[];
    enabledIf?: DbCondition[];
    visibleIfFilter?: DbRecordFilter;
    enabledIfFilter?: DbRecordFilter;
  },
  recordValues: Record<string, unknown>
): ButtonActionResult => {
  const visibleIfConditions = button.visibleIf || [];
  const enabledIfConditions = button.enabledIf || [];

  const actions = button.actions || [];
  const hasActions = actions.length > 0;
  const allActionsConfigured = hasActions && actions.every(isActionConfigReady);

  const isVisibleByConditions = evaluateConditions(visibleIfConditions, recordValues) && evaluateRecordFilter(button.visibleIfFilter, recordValues);
  
  if (!isVisibleByConditions) {
    return {
      state: ButtonState.HIDDEN,
      errors: [],
      visible: false,
      enabled: false,
    };
  }
  
  const isEnabledByConditions = evaluateConditions(enabledIfConditions, recordValues) && evaluateRecordFilter(button.enabledIfFilter, recordValues);

  if (!allActionsConfigured) {
    return {
        state: ButtonState.DISABLED,
        errors: [],
        visible: true,
        enabled: false,
    };
  }

  if (!isEnabledByConditions) {
    return {
      state: ButtonState.DISABLED,
      errors: ["Button is disabled by conditions."],
      visible: true,
      enabled: false,
    };
  }
  
  return {
    state: ButtonState.ENABLED,
    errors: [],
    visible: true,
    enabled: true,
  };
};



export const getButtonActionDefinition = (actionType: string): ButtonAction | undefined => {
  return ButtonActions.getButtonAction(actionType);
};

export const getAvailableActions = (): Array<{value: string, label: string}> => {
  return ButtonActions.getButtonActionIdentifiers().map(identifier => {
    const action = getButtonActionDefinition(identifier);
    return {
      value: identifier,
      label: action?.displayName || identifier
    };
  });
};

export const getDefaultPropsForActionType = (actionType: string): Record<string, unknown> => {
  const action = getButtonActionDefinition(actionType);
  if (!action) return {};
  
  const defaultProps: Record<string, unknown> = {};
  Object.entries(action.props || {}).forEach(([key, prop]) => {
    const propObj = prop as unknown as Record<string, unknown>;
    const propType = propObj.type;
    
    switch (propType) {
      case 'SHORT_TEXT':
      case 'LONG_TEXT':
      case 'DATE':
        defaultProps[key] = '';
        break;
      case 'STATIC_DROPDOWN':
      case 'DROPDOWN':
        defaultProps[key] = '';
        break;
      case 'SWITCH':
        defaultProps[key] = false;
        break;
      case 'NUMBER':
        defaultProps[key] = 0;
        break;
      case 'JSON':
        defaultProps[key] = '{}';
        break;
      case 'KEY_VALUE_ARRAY':
      case 'ARRAY':
        defaultProps[key] = [];
        break;
      default:
        defaultProps[key] = '';
        break;
    }
  });
  
  return defaultProps;
};

export const validateButtonActionConfig = (
  actionIdentifier: string,
  config: Record<string, unknown>
): {valid: boolean; errors: string[]} => {
  const action = getButtonActionDefinition(actionIdentifier);
  if (!action) {
    return {valid: false, errors: [`Unknown action: ${actionIdentifier}`]};
  }

  const errors: string[] = [];

  Object.entries(action.props || {}).forEach(([propKey, prop]) => {
    const propObj = prop as unknown as Record<string, unknown>;
    const isRequired = propObj.required;
    const configValue = config[propKey];
    const isEmpty = !configValue || configValue === '';

    if (isRequired && isEmpty) {
      const errorMsg = `${propObj.displayName || propKey} is required`;
      errors.push(errorMsg);
    }
  });

  const result = {valid: errors.length === 0, errors};
  return result;
};



export interface UseInputDialogReturn {
  inputDialogOpen: boolean;
  inputDialogTitle: string;
  inputDialogMessage: string;
  inputValue: string;
  showInputDialog: (title: string, message: string, callback: (value: string) => void) => void;
  handleInputSubmit: () => void;
  setInputDialogOpen: (open: boolean) => void;
  setInputValue: (value: string) => void;
}

export const useInputDialog = (): UseInputDialogReturn => {
  const [inputDialogOpen, setInputDialogOpen] = useState(false);
  const [inputDialogTitle, setInputDialogTitle] = useState("");
  const [inputDialogMessage, setInputDialogMessage] = useState("");
  const [inputValue, setInputValue] = useState("");
  const [inputCallback, setInputCallback] = useState<((value: string) => void) | null>(null);

  const showInputDialog = (title: string, message: string, callback: (value: string) => void) => {
    setInputDialogTitle(title);
    setInputDialogMessage(message);
    setInputValue("");
    setInputCallback(() => callback);
    setInputDialogOpen(true);
  };

  const handleInputSubmit = () => {
    if (inputCallback) {
      inputCallback(inputValue);
    }
    setInputDialogOpen(false);
  };

  return {
    inputDialogOpen,
    inputDialogTitle,
    inputDialogMessage,
    inputValue,
    showInputDialog,
    handleInputSubmit,
    setInputDialogOpen,
    setInputValue
  };
};

export const createActionServices = (
  hooks: {
    updateRecordValues: (databaseId: string, recordIds: string[], values: Record<string, any>) => Promise<boolean>;
    deleteRecords: (databaseId: string, ids: string[]) => Promise<void>;
    directDeleteRecords?: (databaseId: string, ids: string[]) => Promise<void>;
    setPeekRecord?: (recordId: string, databaseId: string) => void;
    pushPeek?: (recordId: string, databaseId: string) => void;
    confirm: ActionServices['confirm'];
    toast: ActionServices['toast'];
    router: ActionServices['router'];
    forceRender: () => void;
    sendMessage: (message: string) => void;
  },
  inputDialog: UseInputDialogReturn
): ActionServices => {
  return {
    updateRecordValues: hooks.updateRecordValues,
    deleteRecords: hooks.deleteRecords,
    directDeleteRecords: hooks.directDeleteRecords || hooks.deleteRecords, 
    setPeekRecord: hooks.setPeekRecord,
    pushPeek: hooks.pushPeek,
    confirm: hooks.confirm,
    toast: hooks.toast,
    router: hooks.router,
    forceRender: hooks.forceRender,
    sendMessage: hooks.sendMessage,
    showInputDialog: inputDialog.showInputDialog
  };
};
