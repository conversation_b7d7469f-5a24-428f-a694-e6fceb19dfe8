"use client";

import { useViews } from "@/providers/views";
import { usePeekStack } from "@/providers/peekStack";
import { useMaybeRecord } from "@/providers/record";

/**
 * Hook that intelligently handles record navigation based on peek context.
 * If we're in any record context (regular peek or stacked), it stacks a new peek.
 * If we're not, it uses the regular peek functionality.
 */
export const useStackedPeek = () => {
    const { setPeekRecordId } = useViews();
    const { isInPeekContext, pushPeek } = usePeekStack();
    const maybeRecord = useMaybeRecord();

    const openRecord = (recordId: string, databaseId: string) => {
        console.log('🔍 useStackedPeek.openRecord called:', {
            recordId,
            databaseId,
            maybeRecord: maybeRecord ? {
                id: maybeRecord.recordInfo.record.id,
                databaseId: maybeRecord.recordInfo.record.databaseId
            } : null,
            isInPeekContext,
            timestamp: new Date().toISOString()
        });

        // Always try to push a new peek if a record ID is provided
        if (recordId) {
            // We're in a record context if we have a record OR if we're already in stacked peek context
            const inRecordContext = !!maybeRecord || isInPeekContext;

            console.log('🎯 Context analysis:', {
                inRecordContext,
                hasMaybeRecord: !!maybeRecord,
                isInPeekContext,
                decision: inRecordContext ? 'PUSH_PEEK' : 'SET_PEEK_RECORD_ID'
            });

            if (inRecordContext) {
                // We're in a record context (record peek or stacked peek), so stack a new peek
                console.log('📚 Pushing to peek stack:', { recordId, databaseId });
                pushPeek(recordId, databaseId);
            } else {
                // We're in a regular view context, use the normal peek functionality
                // Ensure we set both record and database IDs
                console.log('👁️ Setting regular peek record:', { recordId });
                setPeekRecordId(recordId);
            }
        } else {
            console.log('❌ No recordId provided to openRecord');
        }
    };

    return {
        openRecord,
        isInPeekContext: !!maybeRecord || isInPeekContext
    };
}; 