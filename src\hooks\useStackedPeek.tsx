"use client";

import { useViews } from "@/providers/views";
import { usePeekStack } from "@/providers/peekStack";
import { useMaybeRecord } from "@/providers/record";


export const useStackedPeek = (currentDatabaseId?: string) => {
    const { setPeekRecordId } = useViews();
    const { isInPeekContext, pushPeek } = usePeekStack();
    const maybeRecord = useMaybeRecord();

    const openRecord = (recordId: string, databaseId: string, parentRecord?: {id: string, databaseId: string}) => {
        if (currentDatabaseId && databaseId !== currentDatabaseId) {
            pushPeek(recordId, databaseId);
            return;
        }

        
        const inRecordContext = !!parentRecord || !!maybeRecord || isInPeekContext;
        
        if (inRecordContext) {
            pushPeek(recordId, databaseId);
        } else {
            setPeekRecordId(recordId);
        }
    };

    return {
        openRecord,
        isInPeekContext: !!maybeRecord || isInPeekContext
    };
}; 